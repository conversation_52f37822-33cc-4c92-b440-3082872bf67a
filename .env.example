# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/unfurl_dev?schema=public"

# Authentication
BETTER_AUTH_URL=http://localhost:3000
BETTER_AUTH_SECRET=TUw0edfCM2p1MJy2tLrIIRaSM0XZGvXs
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# File Storage (AWS S3 or similar)
# AWS_S3_LOCAL=true
# AWS_S3_LOCAL_ENDPOINT="http://localhost:8000"
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="unfurl-documents"

# AI Services (for document processing)
OPENAI_API_KEY="your-openai-api-key"
AWS_TEXTRACT_REGION="us-east-1"

# Stripe (for payments)
STRIPE_SECRET_KEY="your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"
STRIPE_WEBHOOK_SECRET="your-stripe-webhook-secret"

# Qstash (for background jobs)
CRON_SECRET="your-cron-secret"
WORKER_SECRET="your-queue-secret"
QSTASH_TOKEN=eyJVc2VySUQiOiJkZWZhdWx0VXNlciIsIlBhc3N3b3JkIjoiZGVmYXVsdFBhc3N3b3JkIn0=