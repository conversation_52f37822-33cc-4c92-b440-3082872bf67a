'use server';

import { requireUser } from '@/lib/auth';
import { apiKeyOperations, type APIKey } from '@/lib/db';
import { randomBytes } from 'crypto';

interface NewAPIKeyResult {
  id: string;
  key: string;
  name: string;
  createdAt: string;
  lastUsedAt: string | null;
  keyPreview: string;
  message: string;
}

interface ActionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export async function createAPIKey(
  name: string,
): Promise<ActionResult<NewAPIKeyResult>> {
  try {
    const user = await requireUser();

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return {
        success: false,
        error: 'API key name is required',
      };
    }

    if (name.trim().length > 50) {
      return {
        success: false,
        error: 'API key name must be 50 characters or less',
      };
    }

    // Check if user already has too many API keys (limit to 10)
    const existingKeys = await apiKeyOperations.findByUser(user.id);
    if (existingKeys.length >= 10) {
      return {
        success: false,
        error: 'Maximum of 10 API keys allowed per user',
      };
    }

    // Generate a secure API key
    const keyBytes = randomBytes(32);
    const apiKey = `unfurl_${keyBytes.toString('hex')}`;

    // Create the API key in the database
    const newAPIKey = await apiKeyOperations.create({
      name: name.trim(),
      key: apiKey,
      userId: user.id,
    });

    // Return the new API key (this is the only time we'll show the full key)
    return {
      success: true,
      data: {
        id: newAPIKey.id,
        name: newAPIKey.name,
        key: newAPIKey.key,
        createdAt: newAPIKey.createdAt.toISOString(),
        lastUsedAt: newAPIKey.lastUsedAt
          ? newAPIKey.lastUsedAt.toISOString()
          : null,
        keyPreview: `${newAPIKey.key.slice(0, 12)}...${newAPIKey.key.slice(-4)}`,
        message:
          "API key created successfully. Make sure to copy it now - you won't be able to see it again!",
      },
    };
  } catch (error) {
    console.error('Error creating API key:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return {
        success: false,
        error: 'Unauthorized',
      };
    }

    return {
      success: false,
      error: 'Failed to create API key',
    };
  }
}

export async function deleteAPIKey(
  keyId: string,
): Promise<ActionResult<{ message: string }>> {
  try {
    const user = await requireUser();

    if (!keyId) {
      return {
        success: false,
        error: 'API key ID is required',
      };
    }

    // First, verify the API key belongs to the current user
    const existingKeys = await apiKeyOperations.findByUser(user.id);
    const keyToDelete = existingKeys.find((key: APIKey) => key.id === keyId);

    if (!keyToDelete) {
      return {
        success: false,
        error: 'API key not found',
      };
    }

    // Delete the API key
    await apiKeyOperations.delete(keyId);

    return {
      success: true,
      data: { message: 'API key deleted successfully' },
    };
  } catch (error) {
    console.error('Error deleting API key:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return {
        success: false,
        error: 'Unauthorized',
      };
    }

    return {
      success: false,
      error: 'Failed to delete API key',
    };
  }
}
