'use server';

import { requireUser } from '@/lib/auth';
import { userOperations } from '@/lib/db';
import { UserSchema } from '@/prisma/generated/zod';
import z from 'zod';

export async function getUserProfile() {
  const user = await requireUser();

  return user;
}

export async function updateProfile(data: z.infer<typeof UserSchema>) {
  const user = await requireUser();

  try {
    await userOperations.update(user.id, data);

    return {
      values: {
        text: 'Successfully updated profile.',
      },
      redirect: '/dashboard',
    };
  } catch (e) {
    const error = e as Error;
    return {
      errors: { message: [error.message || 'An unknown error occurred'] },
    };
  }
}
