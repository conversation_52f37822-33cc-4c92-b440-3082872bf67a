import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';

import { ForgotPasswordForm } from '@/components/auth/forgot-password-form';
import { Button } from '@/components/ui/button';
import { LogoWithTitle } from '@/components/logo-with-title';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Forgot Password',
};

export default function ForgotPasswordPage() {
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
      <Link href="/login" className="absolute top-5 left-5">
        <Button variant="ghost">
          <ChevronLeft />
          back
        </Button>
      </Link>

      <div className="flex w-full max-w-sm flex-col gap-6">
        <Link href="/" className="block">
          <LogoWithTitle />
        </Link>
        <ForgotPasswordForm />
      </div>
    </div>
  );
}
