import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';

import { LoginForm } from '@/components/auth/login-form';
import { Button } from '@/components/ui/button';
import { Metadata } from 'next';
import { LogoWithTitle } from '@/components/logo-with-title';

export const metadata: Metadata = {
  title: 'Login',
};

export default function LoginPage() {
  return (
    <div className="bg-muted flex min-h-svh flex-col items-center justify-center gap-6 p-6 md:p-10">
      <Link href="/" className="absolute top-5 left-5">
        <Button variant="ghost">
          <ChevronLeft />
          back
        </Button>
      </Link>

      <div className="flex w-full max-w-sm flex-col gap-6">
        <Link href="/" className="block">
          <LogoWithTitle />
        </Link>
        <LoginForm />
      </div>
    </div>
  );
}
