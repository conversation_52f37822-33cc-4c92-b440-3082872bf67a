import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { WebHeader } from '@/components/web/header';
import { WebFooter } from '@/components/web/footer';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneLight } from 'react-syntax-highlighter/dist/cjs/styles/prism';

interface EndpointDocProps {
  method: string;
  path: string;
  description: string;
  headers?: string;
  query?: string;
  body?: string;
  response?: string;
}

const CodeBlock: React.FC<{ code: string; language: string }> = ({
  code,
  language,
}) => (
  <SyntaxHighlighter
    language={language}
    style={oneLight}
    customStyle={{ borderRadius: 8, fontSize: 13, margin: 0 }}
    wrapLongLines
  >
    {code}
  </SyntaxHighlighter>
);

const EndpointDoc: React.FC<EndpointDocProps> = ({
  method,
  path,
  description,
  headers,
  query,
  body,
  response,
}) => {
  return (
    <Card className="mb-8 p-6 shadow-lg border border-gray-200 bg-white/90">
      <div className="flex items-center gap-3 mb-2">
        <Badge className="text-xs px-2 py-1 uppercase font-bold bg-primary text-white">
          {method}
        </Badge>
        <span className="font-mono text-base text-gray-900">{path}</span>
      </div>
      <div className="mb-2 text-gray-700">{description}</div>
      {headers && (
        <div className="mb-2">
          <div className="font-semibold text-sm">Headers:</div>
          <CodeBlock code={headers} language="http" />
        </div>
      )}
      {query && (
        <div className="mb-2">
          <div className="font-semibold text-sm">Query Parameters:</div>
          <CodeBlock code={query} language="http" />
        </div>
      )}
      {body && (
        <div className="mb-2">
          <div className="font-semibold text-sm">Body:</div>
          <CodeBlock code={body} language="json" />
        </div>
      )}
      {response && (
        <div>
          <div className="font-semibold text-sm">Response:</div>
          <CodeBlock code={response} language="json" />
        </div>
      )}
    </Card>
  );
};

const ApiDocsPage: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-gray-50 to-white">
      <WebHeader />
      <main className="flex-1 flex flex-col items-center justify-start py-12 px-2 sm:px-6 lg:px-8">
        <div className="w-full max-w-3xl bg-white/95 rounded-2xl shadow-xl border border-gray-200 p-8 mb-12">
          <h1 className="text-4xl font-bold mb-4 text-primary">
            Unfurl API v1 Documentation
          </h1>
          <p className="mb-8 text-lg text-gray-700">
            Welcome to the Unfurl public API. All endpoints require
            authentication via API key. Base URL:{' '}
            <code className="bg-gray-100 px-2 py-1 rounded">/api/v1</code>
          </p>
          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-2">Authentication</h2>
            <p className="mb-2">
              Include your API key in one of the following headers:
            </p>
            <CodeBlock code={`X-API-Key: your-api-key`} language="http" />
            <div className="h-2" />
            <CodeBlock
              code={`Authorization: Bearer your-api-key`}
              language="http"
            />
            <p className="text-sm text-gray-600 mt-2">
              You can create and manage API keys in your dashboard.
            </p>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-2">Webhooks</h2>
            <p className="mb-2">
              Webhooks provide a way for Unfurl to notify your application about
              events that happen in your account, such as a job completing or
              failing. When an event occurs, Unfurl sends an HTTP POST request
              to the URL you configured for that event.
            </p>
            <h3 className="text-xl font-semibold mb-2">Receiving Webhooks</h3>
            <p className="mb-2">
              To receive webhooks, you need to set up an HTTP endpoint on your
              server that can accept POST requests.
            </p>
            <h3 className="text-xl font-semibold mb-2">
              Verifying Webhook Signatures
            </h3>
            <p className="mb-2">
              For security, Unfurl signs each webhook event with a unique
              signature. You should verify this signature to ensure that the
              webhook request originated from Unfurl and has not been tampered
              with. The signature is included in the{' '}
              <code>X-Unfurl-Signature</code> header.
            </p>
            <p className="mb-2">
              The signature is generated using HMAC-SHA256 with your
              webhook&apos;s secret key. You can find your webhook&apos;s secret
              key in your Unfurl dashboard when you create or view a webhook
              endpoint.
            </p>
            <p className="mb-4">
              Here&apos;s how you can verify the signature in different
              programming languages:
            </p>

            <Tabs defaultValue="javascript">
              <TabsList>
                <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                <TabsTrigger value="php">PHP</TabsTrigger>
                <TabsTrigger value="python">Python</TabsTrigger>
              </TabsList>
              <TabsContent value="javascript">
                <CodeBlock
                  code={`const crypto = require('crypto');\n\nfunction verifyWebhookSignature(payload, signature, secret) {\n  const hmac = crypto.createHmac('sha256', secret);\n  hmac.update(JSON.stringify(payload));\n  const digest = hmac.digest('hex');\n  return digest === signature;\n}\n\n// Example usage in an Express.js route\napp.post('/your-webhook-endpoint', (req, res) => {\n  const signature = req.headers['x-unfurl-signature'];\n  const secret = process.env.UNFURL_WEBHOOK_SECRET; // Your webhook secret\n  const payload = req.body;\n\n  if (!verifyWebhookSignature(payload, signature, secret)) {\n    return res.status(401).send('Invalid signature');\n  }\n\n  // Process the webhook event\n  console.log('Received webhook event:', payload.eventType, payload);\n  res.status(200).send('Webhook received');\n});`}
                  language="javascript"
                />
              </TabsContent>
              <TabsContent value="php">
                <CodeBlock
                  code={`<?php\nfunction verifyWebhookSignature($payload, $signature, $secret) {\n    $hash = hash_hmac('sha256', json_encode($payload), $secret);\n    return hash_equals($hash, $signature);\n}\n\n$payload = json_decode(file_get_contents('php://input'), true);\n$signature = $_SERVER['HTTP_X_UNFURL_SIGNATURE'];\n$secret = getenv('UNFURL_WEBHOOK_SECRET'); // Your webhook secret\n\nif (!verifyWebhookSignature($payload, $signature, $secret)) {\n    http_response_code(401);\n    die('Invalid signature');\n}\n\n// Process the webhook event\nerror_log('Received webhook event: ' . $payload['eventType'] . ' ' . json_encode($payload));\nhttp_response_code(200);\necho 'Webhook received';\n?>`}
                  language="php"
                />
              </TabsContent>
              <TabsContent value="python">
                <CodeBlock
                  code={`import hmac\nimport hashlib\nimport json\nimport os\n\ndef verify_webhook_signature(payload, signature, secret):\n    hashed = hmac.new(secret.encode('utf-8'), json.dumps(payload).encode('utf-8'), hashlib.sha256)\n    return hmac.compare_digest(hashed.hexdigest(), signature)\n\n# Example usage in a Flask route\nfrom flask import Flask, request\n\napp = Flask(__name__)\n\<EMAIL>('/your-webhook-endpoint', methods=['POST'])\ndef webhook_receiver():\n    signature = request.headers.get('X-Unfurl-Signature')\n    secret = os.environ.get('UNFURL_WEBHOOK_SECRET') # Your webhook secret\n    payload = request.json\n\n    if not verify_webhook_signature(payload, signature, secret):\n        return 'Invalid signature', 401\n\n    # Process the webhook event\n    print(f"Received webhook event: {payload['eventType']} {payload}")\n    return 'Webhook received', 200\n\nif __name__ == '__main__':\n    app.run(port=5000)`}
                  language="python"
                />
              </TabsContent>
            </Tabs>
          </section>

          {/* API Health Check */}
          <EndpointDoc
            method="GET"
            path="/api/v1/"
            description="API status and health check endpoint. No authentication required."
            response={`{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    ...
  }
}`}
          />
          {/* Jobs endpoints */}
          <EndpointDoc
            method="GET"
            path="/api/v1/jobs"
            description="Get all jobs for the authenticated user. Supports pagination and status filtering."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            query={`limit: number (optional, default: 50, max: 100)\noffset: number (optional, default: 0)\nstatus: string (optional: PENDING, PROCESSING, COMPLETED, FAILED)`}
            response={`{
  "success": true,
  "data": {
    "jobs": [...],
    "total": 123,
    "limit": 50,
    "offset": 0
  }
}`}
          />
          <EndpointDoc
            method="POST"
            path="/api/v1/jobs"
            description="Upload a document for processing. Accepts multipart/form-data."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            body={`file: File (required)\ntemplateId: string (optional)`}
            response={`{
  "success": true,
  "data": {
    "job": { ... }
  }
}`}
          />
          <EndpointDoc
            method="GET"
            path="/api/v1/jobs/{id}"
            description="Get a specific job by ID."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            response={`{
  "success": true,
  "data": {
    "job": { ... }
  }
}`}
          />
          <EndpointDoc
            method="PUT"
            path="/api/v1/jobs/{id}"
            description="Update a specific job."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            body={`fileName: string (optional)\ntemplateId: string (optional)`}
            response={`{
  "success": true,
  "data": {
    "job": { ... }
  }
}`}
          />
          <EndpointDoc
            method="DELETE"
            path="/api/v1/jobs/{id}"
            description="Delete a specific job."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            response={`{
  "success": true
}`}
          />
          {/* Templates endpoints */}
          <EndpointDoc
            method="GET"
            path="/api/v1/templates"
            description="Get all available templates (public + user's custom templates). Supports pagination, public filter, and search."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            query={`limit: number (optional, default: 50, max: 100)\noffset: number (optional, default: 0)\nisPublic: boolean (optional)\nsearch: string (optional)`}
            response={`{
  "success": true,
  "data": {
    "templates": [...],
    "total": 123,
    "limit": 50,
    "offset": 0
  }
}`}
          />
          <EndpointDoc
            method="POST"
            path="/api/v1/templates"
            description="Create a new custom template."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            body={`name: string (required)\ndescription: string (optional)\ndefinition: object (required)`}
            response={`{
  "success": true,
  "data": {
    "template": { ... }
  }
}`}
          />
          {/* Template management endpoints */}
          <EndpointDoc
            method="GET"
            path="/api/v1/templates/{id}"
            description="Get a specific template by ID (public or user's own)."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            response={`{
  "success": true,
  "data": {
    "template": { ... }
  }
}`}
          />
          <EndpointDoc
            method="PATCH"
            path="/api/v1/templates/{id}"
            description="Update a specific template (only if owned by user)."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            body={`name: string (optional)\ndescription: string (optional)\ndefinition: object (optional)`}
            response={`{
  "success": true,
  "data": {
    "template": { ... }
  }
}`}
          />
          <EndpointDoc
            method="DELETE"
            path="/api/v1/templates/{id}"
            description="Delete a specific template (only if owned by user)."
            headers={`X-API-Key: your-api-key\nAuthorization: Bearer your-api-key`}
            response={`{
  "success": true
}`}
          />
        </div>
      </main>
      <WebFooter />
    </div>
  );
};

export default ApiDocsPage;
