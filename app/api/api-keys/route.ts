import { NextResponse } from 'next/server';
import { requireUser } from '@/lib/auth';
import { apiKeyOperations } from '@/lib/db/api-keys';
import type { APIKey } from '@/lib/db/types';

// GET /api/api-keys - Get all API keys for the current user
export async function GET() {
  try {
    const user = await requireUser();

    const apiKeys = await apiKeyOperations.findByUser(user.id);

    // Don't return the actual key values for security
    const safeAPIKeys = apiKeys.map((key: APIKey) => ({
      id: key.id,
      name: key.name,
      createdAt: key.createdAt,
      lastUsedAt: key.lastUsedAt,
      // Only show first 12 and last 4 characters of the key
      keyPreview: `${key.key.slice(0, 12)}...${key.key.slice(-4)}`,
    }));

    return NextResponse.json({
      success: true,
      data: safeAPIKeys,
    });
  } catch (error) {
    console.error('Error fetching API keys:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
