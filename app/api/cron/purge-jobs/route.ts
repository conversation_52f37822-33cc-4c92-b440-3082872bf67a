import { prisma } from '@/lib/prisma';
import { deleteFileFromUrl } from '@/lib/s3';
import { NextRequest, NextResponse } from 'next/server';

// --- API Route Handler for the Cron Job ---
export async function GET(request: NextRequest) {
  // 1. Secure the endpoint so only an authorized service can run it.
  const authHeader = request.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  console.log('Starting daily job purge...');
  let purgedJobsCount = 0;

  try {
    // 2. Find all users to process their jobs individually.
    const users = await prisma.user.findMany();

    for (const user of users) {
      const retentionDays = user.dataRetentionDays;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      // 3. Find all jobs for the user that are completed and older than their retention period.
      const expiredJobs = await prisma.job.findMany({
        where: {
          userId: user.id,
          status: 'COMPLETED',
          completedAt: {
            lt: cutoffDate, // Less than the cutoff date (i.e., expired)
          },
        },
      });

      if (expiredJobs.length === 0) {
        continue;
      }

      console.log(
        `Found ${expiredJobs.length} expired jobs for user ${user.id}...`,
      );

      // 4. Process each expired job.
      for (const job of expiredJobs) {
        // 4a. Delete the associated file from S3 if the URL exists.
        try {
          await deleteFileFromUrl(job.fileUrl);
          console.log(`Deleted file ${job.fileUrl} from S3 for job ${job.id}.`);
        } catch (s3Error) {
          console.error(
            `Failed to delete S3 object ${job.fileUrl} for job ${job.id}:`,
            s3Error,
          );
          // We continue to purge the DB record even if S3 deletion fails,
          // to ensure data is not accessible to the user.
        }

        // 4b. Update the job record in the database.
        await prisma.job.update({
          where: { id: job.id },
          data: {
            status: 'PURGED',
            extractedData: null, // Set the sensitive data to null
          },
        });
        purgedJobsCount++;
      }
    }

    console.log(`Successfully purged ${purgedJobsCount} jobs.`);
    return NextResponse.json({
      success: true,
      message: `Purge completed. ${purgedJobsCount} jobs processed.`,
    });
  } catch (error: unknown) {
    console.error('Error during purge cron job:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred during the purge process.',
      },
      { status: 500 },
    );
  }
}
