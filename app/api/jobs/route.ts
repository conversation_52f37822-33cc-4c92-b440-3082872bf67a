import { NextResponse } from 'next/server';
import { requireUser } from '@/lib/auth';
import { jobOperations } from '@/lib/db';

export async function GET() {
  try {
    const user = await requireUser();
    const jobs = await jobOperations.findByUser(user.id);

    return NextResponse.json({
      success: true,
      data: jobs,
    });
  } catch (error) {
    console.error('Error fetching jobs:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
