import { NextRequest, NextResponse } from 'next/server';
import { requireUser } from '@/lib/auth';
import { templateOperations } from '@/lib/db/templates';

export async function GET(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await requireUser();
    const { id } = params;

    const template = await templateOperations.findByIdAndUserId(id, user.id);

    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error('Error fetching template:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function PUT(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await requireUser();
    const { id } = params;
    const body = await req.json();

    const { name, description, definition } = body;

    // Validate required fields
    if (!name && !description && !definition) {
      return NextResponse.json(
        {
          success: false,
          error: 'At least one field must be provided for update',
        },
        { status: 400 },
      );
    }

    const updateData: {
      name?: string;
      description?: string;
      definition?: Record<string, unknown>;
    } = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (definition !== undefined) updateData.definition = definition;

    const template = await templateOperations.update(id, updateData, user.id);

    return NextResponse.json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error('Error updating template:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 },
      );
    }

    if (
      error instanceof Error &&
      error.message === 'Template not found or not editable'
    ) {
      return NextResponse.json(
        { success: false, error: 'Template not found or not editable' },
        { status: 404 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

export async function DELETE(req: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const user = await requireUser();
    const { id } = params;

    await templateOperations.delete(id, user.id);

    return NextResponse.json({
      success: true,
      message: 'Template deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting template:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 },
      );
    }

    if (
      error instanceof Error &&
      error.message === 'Template not found or not deletable'
    ) {
      return NextResponse.json(
        { success: false, error: 'Template not found or not deletable' },
        { status: 404 },
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
