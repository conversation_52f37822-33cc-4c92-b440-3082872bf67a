import { NextRequest, NextResponse } from 'next/server';
import { requireApi<PERSON><PERSON><PERSON><PERSON> } from '@/lib/api-auth';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/v1/jobs/[id]
 *
 * Get a specific job by ID
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "job": {...}
 *     }
 *   }
 */
export async function GET(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    const jobId = params.id;

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 },
      );
    }

    // Find job and ensure it belongs to the authenticated user
    const job = await prisma.job.findFirst({
      where: {
        id: jobId,
        userId: user.id,
      },
      include: {
        template: true,
      },
    });

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 },
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        job,
      },
    });
  } catch (error) {
    console.error('Error fetching job via API:', error);

    // Handle authentication errors
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * PUT /api/v1/jobs/[id]
 *
 * Update a specific job
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Body:
 *   {
 *     "fileName": "new-filename.pdf",
 *     "templateId": "template-id" // optional
 *   }
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "document": {...}
 *     }
 *   }
 */
export async function PUT(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    const jobId = params.id;

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 },
      );
    }

    // Parse request body
    const body = await request.json();
    const { fileName, templateId } = body;

    // Verify document exists and belongs to user
    const existingJob = await prisma.job.findFirst({
      where: {
        id: jobId,
        userId: user.id,
      },
    });

    if (!existingJob) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 },
      );
    }

    // Build update data
    const updateData: { fileName?: string; templateId?: string | null } = {};
    if (fileName) updateData.fileName = fileName;
    if (templateId !== undefined) updateData.templateId = templateId || null;

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 },
      );
    }

    // Update job
    const job = await prisma.job.update({
      where: { id: jobId },
      data: updateData,
      include: {
        template: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        job,
      },
    });
  } catch (error) {
    console.error('Error updating job via API:', error);

    // Handle authentication errors
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * DELETE /api/v1/jobs/[id]
 *
 * Delete a specific job
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Response:
 *   {
 *     "success": true,
 *     "message": "Job deleted successfully"
 *   }
 */
export async function DELETE(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    const jobId = params.id;

    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 },
      );
    }

    // Verify document exists and belongs to user
    const existingJob = await prisma.job.findFirst({
      where: {
        id: jobId,
        userId: user.id,
      },
    });

    if (!existingJob) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 },
      );
    }

    // Delete job
    await prisma.job.delete({
      where: { id: jobId },
    });

    return NextResponse.json({
      success: true,
      message: 'Job deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting job via API:', error);

    // Handle authentication errors
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
