import { NextRequest, NextResponse } from 'next/server';
import { requireApiKeyAuth } from '@/lib/api-auth';
import { jobOperations } from '@/lib/db';
import {
  uploadFileToS3,
  generateFileKey,
  validateFile,
  getFileUrl,
} from '@/lib/s3';
import { prisma } from '@/lib/prisma';
import { addJobToQueue } from '@/lib/actions/jobs';
import { JobStatus } from '@/lib/db/jobs';
import { checkCredits } from '@/lib/utils';

/**
 * GET /api/v1/jobs
 *
 * Get all jobs for the authenticated user
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Query Parameters:
 *   - limit: number (optional, default: 50, max: 100)
 *   - offset: number (optional, default: 0)
 *   - status: string (optional, filter by status: UPLOADED, PROCESSING, COMPLETED, FAILED)
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "jobs": [...],
 *       "total": 123,
 *       "limit": 50,
 *       "offset": 0
 *     }
 *   }
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');

    // Build filter options
    const filters: Record<string, unknown> = { userId: user.id };
    if (
      status &&
      ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'].includes(status)
    ) {
      filters.status = status;
    }

    // Get jobs with pagination
    const jobs = await jobOperations.findByUserWithPagination(user.id, {
      limit,
      offset,
      filters,
    });

    // Get total count for pagination
    const totalCount = await jobOperations.countByUser(user.id, filters);

    return NextResponse.json({
      success: true,
      data: {
        jobs,
        total: totalCount,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching jobs via API:', error);

    // Handle authentication errors
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * POST /api/v1/jobs
 *
 * Upload a document using API key authentication
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Body (multipart/form-data):
 *   - file: File blob
 *   - templateId: string (optional)
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "job": {
 *         "id": "job-id",
 *         "fileName": "document.pdf",
 *         "fileUrl": "https://s3.amazonaws.com/...",
 *         ...
 *         "status": "PENDING",
 *         ...
 *         "userId": "user-id",
 *         "templateId": "template-id",
 *         ...
 *         "createdAt": "2023-01-01T00:00:00.000Z",
 *         "updatedAt": "2023-01-01T00:00:00.000Z"
 *       }
 *     }
 *   }
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    // Check if user has credits
    checkCredits(user);

    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const templateId = formData.get('templateId') as string | null;

    if (!file) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required field: file',
        },
        { status: 400 },
      );
    }

    // Extract file information
    const fileName = file.name;
    const fileType = file.type;
    const fileSize = file.size;

    // Validate file
    try {
      validateFile({ type: fileType, size: fileSize });
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: error instanceof Error ? error.message : 'Invalid file',
        },
        { status: 400 },
      );
    }

    // Create a Job record in the database first
    // This gives us a unique jobId to use in the S3 key.
    const newJob = await prisma.job.create({
      data: {
        userId: user!.id,
        fileName,
        status: 'UPLOADING', // A temporary status while client uploads
        fileUrl: '', // Will be updated after this step
        templateId,
      },
    });

    // Generate unique file key
    const fileKey = generateFileKey(user.id, newJob.id, fileName);

    // Convert file to buffer for upload
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    // Upload file directly to S3
    await uploadFileToS3(fileKey, fileBuffer, fileType);

    // Update the job with the final S3 URL
    await prisma.job.update({
      where: { id: newJob.id },
      data: { fileUrl: getFileUrl(fileKey), status: JobStatus.PENDING },
    });

    await addJobToQueue(newJob.id);

    return NextResponse.json({
      success: true,
      data: {
        job: newJob,
      },
    });
  } catch (error) {
    console.error('Error in jobs endpoint:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }

      if (error.message.includes('credits')) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 402 }, // Payment Required
        );
      }

      if (
        error.message.includes('Invalid file') ||
        error.message.includes('Missing required')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
