import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * GET /api/v1/
 *
 * API status and health check endpoint
 *
 * This endpoint provides basic information about the API status,
 * version, and health metrics. No authentication required.
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "status": "healthy",
 *       "version": "1.0.0",
 *       "timestamp": "2024-01-01T00:00:00.000Z",
 *       "uptime": 12345,
 *       "database": "connected",
 *       "features": [...],
 *       "endpoints": [...]
 *     }
 *   }
 */
export async function GET() {
  try {
    // Check database connectivity
    let databaseStatus = 'connected';
    let databaseError = null;

    try {
      await prisma.$queryRaw`SELECT 1`;
    } catch (error) {
      databaseStatus = 'disconnected';
      databaseError = error instanceof Error ? error.message : 'Unknown error';
    }

    // Calculate uptime (simplified - in real app you'd track actual start time)
    const uptime = process.uptime();

    // API feature list
    const features = [
      'Document Upload',
      'Document Management',
      'Job Processing',
      'Template Management',
      'Data Extraction',
      'API Key Authentication',
      'Credit System',
    ];

    // Available endpoints
    const endpoints = [
      {
        method: 'GET',
        path: '/api/v1/jobs',
        description: 'List all jobs',
      },
      {
        method: 'POST',
        path: '/api/v1/jobs',
        description: 'Upload documents for processing',
      },
      {
        method: 'GET',
        path: '/api/v1/jobs/{id}',
        description: 'Get specific job',
      },
      {
        method: 'PUT',
        path: '/api/v1/jobs/{id}',
        description: 'Update job',
      },
      {
        method: 'DELETE',
        path: '/api/v1/jobs/{id}',
        description: 'Delete job',
      },
      {
        method: 'GET',
        path: '/api/v1/templates',
        description: 'List available templates',
      },
      {
        method: 'POST',
        path: '/api/v1/templates',
        description: 'Create custom template',
      },
    ];

    // Determine overall health status
    const status = databaseStatus === 'connected' ? 'healthy' : 'degraded';

    const response = {
      success: true,
      data: {
        status,
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        uptime: Math.floor(uptime),
        database: databaseStatus,
        ...(databaseError && { databaseError }),
        features,
        endpoints,
        authentication: {
          type: 'API Key',
          headers: ['X-API-Key', 'Authorization: Bearer <key>'],
        },
        limits: {
          maxFileSize: '10MB',
          maxRequestsPerMinute: 1000,
          supportedFormats: ['PDF', 'PNG', 'JPG', 'JPEG'],
        },
        documentation: {
          swagger: '/api/docs',
          readme: 'https://docs.unfurl.ink/api/v1',
        },
      },
    };

    return NextResponse.json(response, {
      status: status === 'healthy' ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    console.error('Error in API health check:', error);

    return NextResponse.json(
      {
        success: false,
        data: {
          status: 'unhealthy',
          version: '1.0.0',
          timestamp: new Date().toISOString(),
          error: 'Health check failed',
          database: 'unknown',
        },
      },
      {
        status: 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      },
    );
  }
}

/**
 * HEAD /api/v1/
 *
 * Simple health check endpoint for monitoring
 * Returns 200 if API is healthy, 503 if not
 */
export async function HEAD() {
  try {
    // Quick database connectivity check
    await prisma.$queryRaw`SELECT 1`;
    return new NextResponse(null, { status: 200 });
  } catch (_error) {
    return new NextResponse(null, { status: 503 });
  }
}
