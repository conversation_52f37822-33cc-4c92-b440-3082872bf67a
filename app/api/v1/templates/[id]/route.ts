import { NextRequest, NextResponse } from 'next/server';
import { requireApi<PERSON>eyAuth } from '@/lib/api-auth';
import { templateOperations } from '@/lib/db/templates';

/**
 * GET /api/v1/templates/[id]
 *
 * Get a specific template by ID (public or user's own)
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 */
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { user } = await requireApiKeyAuth(request);
    const templateId = params.id;
    if (!templateId) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }
    // Allow access if public or owned by user
    const template = await templateOperations.findById(templateId);
    if (!template || (template.userId && template.userId !== user.id)) {
      return NextResponse.json(
        { success: false, error: 'Template not found' },
        { status: 404 }
      );
    }
    return NextResponse.json({ success: true, data: { template } });
  } catch (error) {
    if (
      error instanceof Error &&
      (error.message.includes('API key') ||
        error.message.includes('Authentication'))
    ) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 401 }
      );
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/templates/[id]
 *
 * Update a specific template (only if owned by user)
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 */
export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { user } = await requireApiKeyAuth(request);
    const templateId = params.id;
    if (!templateId) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }
    const body = await request.json();
    const { name, description, definition } = body;
    if (!name && !description && !definition) {
      return NextResponse.json(
        { success: false, error: 'No valid fields to update' },
        { status: 400 }
      );
    }
    const updated = await templateOperations.update(
      templateId,
      { name, description, definition },
      user.id
    );
    return NextResponse.json({ success: true, data: { template: updated } });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('not editable')) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 403 }
        );
      }
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/v1/templates/[id]
 *
 * Delete a specific template (only if owned by user)
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 */
export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  const params = await props.params;
  try {
    const { user } = await requireApiKeyAuth(request);
    const templateId = params.id;
    if (!templateId) {
      return NextResponse.json(
        { success: false, error: 'Template ID is required' },
        { status: 400 }
      );
    }
    await templateOperations.delete(templateId, user.id);
    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('not deletable')) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 403 }
        );
      }
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 }
        );
      }
    }
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
