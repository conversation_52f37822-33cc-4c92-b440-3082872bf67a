import { NextRequest, NextResponse } from 'next/server';
import { requireApi<PERSON><PERSON><PERSON><PERSON> } from '@/lib/api-auth';
import { prisma } from '@/lib/prisma';
import { FIELD_TYPES } from '@/components/template-dialog-shared';

/**
 * GET /api/v1/templates
 *
 * Get all available templates (public + user's custom templates)
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Query Parameters:
 *   - limit: number (optional, default: 50, max: 100)
 *   - offset: number (optional, default: 0)
 *   - isPublic: boolean (optional, filter by public templates)
 *   - search: string (optional, search by name or description)
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "templates": [...],
 *       "total": 123,
 *       "limit": 50,
 *       "offset": 0
 *     }
 *   }
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 100);
    const offset = parseInt(searchParams.get('offset') || '0');
    const isPublic = searchParams.get('isPublic');
    const search = searchParams.get('search');

    // Build filter options
    const filters: Record<string, unknown> = {
      OR: [
        { userId: null }, // Public templates available to everyone
        { userId: user.id }, // User's own templates
      ],
    };

    // Apply additional filters
    if (isPublic !== null && isPublic !== undefined) {
      const publicFilter = isPublic === 'true';
      if (publicFilter) {
        filters.OR = [{ userId: null }];
      } else {
        filters.OR = [{ userId: user.id }];
      }
    }

    // Apply search filter
    if (search) {
      filters.AND = [
        filters.OR ? { OR: filters.OR } : {},
        {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ],
        },
      ];
      delete filters.OR;
    }

    // Get templates with pagination
    const templates = await prisma.template.findMany({
      where: filters,
      orderBy: [
        { userId: 'asc' }, // Public templates first
        { createdAt: 'desc' },
      ],
      take: limit,
      skip: offset,
      select: {
        id: true,
        name: true,
        description: true,
        definition: true,
        userId: true,
        user: {
          select: {
            name: true,
          },
        },
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            jobs: true,
          },
        },
      },
    });

    // Get total count for pagination
    const totalCount = await prisma.template.count({
      where: filters,
    });

    return NextResponse.json({
      success: true,
      data: {
        templates,
        total: totalCount,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching templates via API:', error);

    // Handle authentication errors
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}

/**
 * POST /api/v1/templates
 *
 * Create a new custom template
 *
 * Headers:
 *   X-API-Key: your-api-key
 *   or
 *   Authorization: Bearer your-api-key
 *
 * Body:
 *   {
 *     "name": "My Custom Template",
 *     "description": "Description of the template",
 *     "definition": {
 *       "field1": {
 *         "type": "text",
 *         "label": "Field 1",
 *         "required": true
 *       },
 *       "field2": {
 *         "type": "number",
 *         "label": "Field 2",
 *         "required": false
 *       }
 *     }
 *   }
 *
 * Response:
 *   {
 *     "success": true,
 *     "data": {
 *       "template": {...}
 *     }
 *   }
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate using API key
    const { user } = await requireApiKeyAuth(request);

    // Parse request body
    const body = await request.json();
    const { name, description, definition } = body;

    if (!name || !definition) {
      return NextResponse.json(
        {
          success: false,
          error: 'Missing required fields: name, definition',
        },
        { status: 400 },
      );
    }

    // Validate definition structure
    if (typeof definition !== 'object' || Array.isArray(definition)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Fields must be an object',
        },
        { status: 400 },
      );
    }

    // Basic validation of field structure
    for (const [fieldKey, fieldValue] of Object.entries(definition)) {
      if (typeof fieldValue !== 'object' || !fieldValue) {
        return NextResponse.json(
          {
            success: false,
            error: `Invalid field configuration for '${fieldKey}'`,
          },
          { status: 400 },
        );
      }

      const field = fieldValue as Record<string, unknown>;
      if (!field.type) {
        return NextResponse.json(
          {
            success: false,
            error: `Field '${fieldKey}' must have 'type' property`,
          },
          { status: 400 },
        );
      }

      // Validate field types
      const allowedTypes = FIELD_TYPES.map((ft) => ft.value);
      if (!allowedTypes.includes(field.type as string)) {
        return NextResponse.json(
          {
            success: false,
            error: `Invalid field type '${field.type}' for field '${fieldKey}'. Allowed types: ${allowedTypes.join(', ')}`,
          },
          { status: 400 },
        );
      }
    }

    // Check if template name already exists for this user
    const existingTemplate = await prisma.template.findFirst({
      where: {
        name,
        userId: user.id,
      },
    });

    if (existingTemplate) {
      return NextResponse.json(
        {
          success: false,
          error: 'A template with this name already exists',
        },
        { status: 409 }, // Conflict
      );
    }

    // Create template
    const template = await prisma.template.create({
      data: {
        name,
        description: description || null,
        definition,
        userId: user.id,
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            jobs: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        template,
      },
    });
  } catch (error) {
    console.error('Error creating template via API:', error);

    // Handle authentication errors
    if (error instanceof Error) {
      if (
        error.message.includes('API key') ||
        error.message.includes('Authentication')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 401 },
        );
      }

      if (
        error.message.includes('Missing required') ||
        error.message.includes('Invalid')
      ) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 },
        );
      }
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 },
    );
  }
}
