import { NextRequest, NextResponse } from 'next/server';
import { jobOperations } from '@/lib/db';
import { processDocumentWithAI } from '@/lib/ai';
import { JobStatus } from '@/lib/db/jobs';
import { prisma } from '@/lib/prisma';
import { sendWebhook } from '@/lib/webhook';
import { Job } from '@prisma/client';

export async function POST(req: NextRequest) {
  // 1. Authenticate the request to ensure it's from a trusted source
  const authHeader = req.headers.get('authorization');
  if (authHeader !== `Bearer ${process.env.WORKER_SECRET}`) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { jobId } = await req.json();

  if (!jobId) {
    return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
  }

  // 2. Fetch the job and related user/template data
  const job = await jobOperations.findById(jobId);

  if (!job || !job.user) {
    return NextResponse.json({ error: 'Job not found' }, { status: 404 });
  }

  await jobOperations.updateStatus(jobId, JobStatus.PROCESSING);

  try {
    // 3. Process the document using the AI service
    const { pageCount, extractedData } = await processDocumentWithAI({
      fileUrl: job.fileUrl,
      templateSchema: job.template?.definition as Record<string, unknown>,
    });

    // 4. Perform the critical transaction: update credits and job status
    // Using a transaction ensures that both operations suceed or both fail.
    let updatedJob: Job | undefined;
    await prisma.$transaction(async (tx) => {
      // Decrement user credits
      await tx.user.update({
        where: { id: job.userId },
        data: {
          credits: { decrement: pageCount },
        },
      });

      // Update the job with the results
      updatedJob = await tx.job.update({
        where: { id: jobId },
        data: {
          status: JobStatus.COMPLETED,
          completedAt: new Date(),
          pageCount: pageCount,
          extractedData: extractedData as Prisma.JsonValue,
        },
      });
    });

    if (updatedJob) {
      await sendWebhook('job.completed', updatedJob, job.userId);
    }

    return NextResponse.json({
      success: true,
      message: `Job ${jobId} completed successfully.`,
    });
  } catch (err: unknown) {
    console.error(`Failed to process job ${jobId}`, err);
    const errorMessage =
      err instanceof Error ? err.message : 'Processing failed';

    const failedJob = await jobOperations.updateStatus(
      jobId,
      JobStatus.FAILED,
      errorMessage,
    );

    if (failedJob) {
      await sendWebhook('job.failed', failedJob, job.userId);
    }
    // TODO: Retry the process

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 },
    );
  }
}
