import { getUserProfile } from '@/actions/user';
import { UserForm } from '@/components/account/user-form';
import { DashboardWrapper } from '@/components/dashboard/wrapper';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Account',
};

export default async function AccountPage() {
  const userProfile = await getUserProfile();

  return (
    <DashboardWrapper
      breadcrumb={[{ title: 'Account', href: '/dashboard/account' }]}
      className="p-6"
    >
      <UserForm defaultValues={userProfile} />
    </DashboardWrapper>
  );
}
