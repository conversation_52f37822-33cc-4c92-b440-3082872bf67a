'use client';

import { useState } from 'react';
import useS<PERSON> from 'swr';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Skeleton } from '@/components/ui/skeleton';
import { Trash2, Key, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { confirm } from '@/hooks/use-one-dialog';
import { deleteAPIKey } from '@/actions/api-keys';

interface APIKey {
  id: string;
  name: string;
  createdAt: string;
  lastUsedAt: string | null;
  keyPreview: string;
}

const fetcher = async (url: string): Promise<APIKey[]> => {
  const response = await fetch(url);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch API keys');
  }

  return result.data;
};

export function APIKeysClientPage() {
  const {
    data: apiKeys,
    error,
    mutate,
    isLoading,
  } = useSWR('/api/api-keys', fetcher);

  const [deleting, setDeleting] = useState<string | null>(null);

  // Delete API key
  const handleDeleteAPIKey = async (key: APIKey) => {
    if (
      !(await confirm({
        title: 'Delete API Key',
        body: `Are you sure you want to delete the API key "${key?.name}"? This action cannot be undone and any applications using this key will lose access immediately.`,
        closeButton: 'Cancel',
        actionButton: 'Delete',
        actionButtonVariant: 'destructive',
      }))
    ) {
      return;
    }

    setDeleting(key.id);
    try {
      const result = await deleteAPIKey(key.id);

      if (result.success) {
        // Refresh the list
        mutate();

        toast.success('API key deleted successfully');
      } else {
        throw new Error(result.error || 'Failed to delete API key');
      }
    } catch (error) {
      console.error('Error deleting API key:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to delete API key',
      );
    } finally {
      setDeleting(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderLoadingState = () => (
    <Card className="p-0 overflow-hidden">
      <Table>
        <TableHeader className="bg-muted sticky top-0 z-10">
          <TableRow>
            <TableHead className="ps-4">Name</TableHead>
            <TableHead>Key Preview</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Last Used</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {[1, 2, 3].map((i) => (
            <TableRow key={i}>
              <TableCell className="ps-4">
                <Skeleton className="h-5 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-5 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-8 w-8" />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </Card>
  );

  return (
    <div>
      {/* Error State */}
      {error ? (
        <Card className="border-red-200 bg-red-50">
          <div className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-red-900 mb-2">
              Failed to Load API Keys
            </h3>
            <p className="text-red-700 mb-4">
              {error instanceof Error
                ? error.message
                : 'An unexpected error occurred'}
            </p>
            <Button
              variant="outline"
              onClick={() => mutate()}
              className="border-red-300 text-red-700 hover:bg-red-100"
            >
              Try Again
            </Button>
          </div>
        </Card>
      ) : isLoading && !apiKeys ? (
        renderLoadingState()
      ) : apiKeys?.length === 0 ? (
        <Card>
          <div className="p-8 text-center">
            <Key className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No API Keys Yet
            </h3>
            <p className="text-gray-600 mb-4">
              Create your first API key to start integrating with Unfurl
              programmatically.
            </p>
          </div>
        </Card>
      ) : (
        <Card className="p-0 overflow-hidden">
          <Table>
            <TableHeader className="bg-muted sticky top-0 z-10">
              <TableRow>
                <TableHead className="ps-4">Name</TableHead>
                <TableHead>Key Preview</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Last Used</TableHead>
                <TableHead className="w-[80px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {apiKeys?.map((key) => (
                <TableRow key={key.id}>
                  <TableCell className="font-medium ps-4">{key.name}</TableCell>
                  <TableCell className="font-mono">{key.keyPreview}</TableCell>
                  <TableCell>{formatDate(key.createdAt)}</TableCell>
                  <TableCell>
                    {key.lastUsedAt ? formatDate(key.lastUsedAt) : 'Never'}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAPIKey(key)}
                      disabled={deleting === key.id}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Usage Info */}
      <div className="mt-8 text-sm text-gray-600">
        <p>
          <strong>API Usage:</strong> You have {apiKeys?.length} of 10 API keys
          created. Each key can make unlimited requests within your plan limits.
        </p>
      </div>
    </div>
  );
}
