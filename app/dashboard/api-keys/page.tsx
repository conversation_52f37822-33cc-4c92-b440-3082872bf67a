import { Metadata } from 'next';
import { APIKeysClientPage } from './client-page';
import { DashboardWrapper } from '@/components/dashboard/wrapper';
import { APIKeyCreateButton } from '@/components/api-keys/create-button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { APIKey, apiKeyOperations } from '@/lib/db';
import { requireUser } from '@/lib/auth';
import { SWRConfig } from 'swr';

export const metadata: Metadata = {
  title: 'API Keys',
};

export default async function APIKeysPage() {
  const user = await requireUser();
  const apiKeys = await apiKeyOperations.findByUser(user.id);
  const safeAPIKeys = apiKeys.map((key: APIKey) => ({
    id: key.id,
    name: key.name,
    createdAt: key.createdAt,
    lastUsedAt: key.lastUsedAt,
    // Only show first 12 and last 4 characters of the key
    keyPreview: `${key.key.slice(0, 12)}...${key.key.slice(-4)}`,
  }));

  return (
    <DashboardWrapper
      breadcrumb={[{ title: 'API Keys', href: '/dashboard/api-keys' }]}
      className="p-6"
    >
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">API Keys</h1>
          <p className="text-gray-600 mt-1">
            Generate and manage API keys for programmatic access to Unfurl
          </p>
        </div>
        <APIKeyCreateButton />
      </div>

      {/* Security Warning */}
      <Alert className="mb-6 border-amber-200 bg-amber-50 text-amber-700">
        <AlertTriangle />
        <AlertTitle>Keep Your API Keys Secure</AlertTitle>
        <AlertDescription className="text-amber-700/90">
          <ul className="list-inside list-disc text-sm">
            <li>
              Never share your API keys publicly or commit them to version
              control
            </li>
            <li>
              Store them securely as environment variables in your applications
            </li>
            <li>
              Regenerate keys immediately if you suspect they&apos;ve been
              compromised
            </li>
            <li>Monitor usage regularly and delete unused keys</li>
          </ul>
        </AlertDescription>
      </Alert>
      <SWRConfig
        value={{
          fallback: {
            '/api/api-keys': safeAPIKeys,
          },
        }}
      >
        <APIKeysClientPage />
      </SWRConfig>
    </DashboardWrapper>
  );
}
