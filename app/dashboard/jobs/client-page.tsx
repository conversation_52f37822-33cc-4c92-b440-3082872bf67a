'use client';

import { DocumentListPanel } from '@/components/jobs/document-list-panel';
import { DocumentViewer } from '@/components/jobs/document-viewer';
import { DataExtractionPanel } from '@/components/jobs/data-extraction-panel';
import { useState } from 'react';

export default function JobsClientPage() {
  const [selectedJobId, setSelectedJobId] = useState<string>();
  const [activeField, setActiveField] = useState<string>();

  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId);
  };

  const handleFieldFocus = (field: string) => {
    setActiveField(field);
  };

  const handleFieldBlur = () => {
    setActiveField(undefined);
  };

  return (
    <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
      <DocumentListPanel
        selectedJobId={selectedJobId}
        onJobSelect={handleJobSelect}
      />
      <DocumentViewer jobId={selectedJobId} activeField={activeField} />
      <DataExtractionPanel
        jobId={selectedJobId}
        onFieldFocus={handleFieldFocus}
        onFieldBlur={handleFieldBlur}
      />
    </div>
  );
}
