import { Metadata } from 'next';
import JobsClientPage from './client-page';
import { DashboardWrapper } from '@/components/dashboard/wrapper';
import { jobOperations } from '@/lib/db';
import { requireUser } from '@/lib/auth';
import { SWRConfig } from 'swr';

export const metadata: Metadata = {
  title: 'Jobs',
};

export default async function JobsPage() {
  const user = await requireUser();
  const jobs = await jobOperations.findByUser(user.id);

  return (
    <DashboardWrapper breadcrumb={[{ title: 'Jobs', href: '/dashboard/jobs' }]}>
      <SWRConfig
        value={{
          fallback: {
            '/api/jobs': jobs,
          },
        }}
      >
        <JobsClientPage />
      </SWRConfig>
    </DashboardWrapper>
  );
}
