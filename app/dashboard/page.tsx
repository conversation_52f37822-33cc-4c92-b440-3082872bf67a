import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Metadata } from 'next';
import { <PERSON> } from '@/components/dashboard/hero';
import { AccountInfo } from '@/components/dashboard/account-info';
import { DashboardWrapper } from '@/components/dashboard/wrapper';
import { Stats } from '@/components/dashboard/stats';
import { StatsChart } from '@/components/dashboard/stats-chart';
import { requireUser } from '@/lib/auth';
import { jobOperations, templateOperations, apiKeyOperations } from '@/lib/db';
import { prisma } from '@/lib/prisma';

export const metadata: Metadata = {
  title: 'Dashboard',
};

function getLast7Days() {
  const days = [];
  for (let i = 6; i >= 0; i--) {
    const d = new Date();
    d.setDate(d.getDate() - i);
    days.push(d.toISOString().slice(0, 10));
  }
  return days;
}

export default async function DashboardPage() {
  const user = await requireUser();

  // Stats
  const [jobsCount, templates, apiKeys, webhooks] = await Promise.all([
    jobOperations.countByUser(user.id),
    templateOperations.findByUser(user.id),
    apiKeyOperations.findByUser(user.id),
    prisma.webhookEndpoint.findMany({ where: { userId: user.id } }),
  ]);

  // Chart data: jobs per day for last 7 days
  const last7Days = getLast7Days();
  const jobs = await jobOperations.findByUser(user.id, 100, 0); // get recent jobs
  const jobsByDay: Record<string, number> = {};
  for (const day of last7Days) jobsByDay[day] = 0;
  for (const job of jobs) {
    const day = job.createdAt.toISOString().slice(0, 10);
    if (jobsByDay[day] !== undefined) jobsByDay[day]++;
  }
  const chartData = last7Days.map((date) => ({ date, count: jobsByDay[date] }));

  return (
    <DashboardWrapper className="p-6 space-y-10">
      <Hero />
      <section>
        <Stats
          jobs={jobsCount}
          templates={templates.length}
          apiKeys={apiKeys.length}
          webhooks={webhooks.length}
        />
        <StatsChart data={chartData} />
      </section>
      <section>
        <h2 className="text-xl font-semibold mb-4">Your Account</h2>
        <AccountInfo />
      </section>
      <section>
        <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2">View Templates</h3>
              <p className="text-gray-600 mb-4">
                Manage your data extraction templates
              </p>
            </div>
            <Button asChild className="w-full mt-auto">
              <Link href="/templates">View Templates</Link>
            </Button>
          </div>
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2">Upload Documents</h3>
              <p className="text-gray-600 mb-4">
                Start extracting data from your documents
              </p>
            </div>
            <Button asChild className="w-full mt-auto">
              <Link href="/jobs">Go to Extraction Jobs</Link>
            </Button>
          </div>
          <div className="bg-white rounded-lg shadow p-6 flex flex-col justify-between">
            <div>
              <h3 className="text-lg font-semibold mb-2">API Access</h3>
              <p className="text-gray-600 mb-4">
                Generate API keys for integration
              </p>
            </div>
            <Button asChild className="w-full mt-auto">
              <Link href="/api-keys">Manage API Keys</Link>
            </Button>
          </div>
        </div>
      </section>
    </DashboardWrapper>
  );
}
