'use client';

import { useState } from 'react';
import useS<PERSON> from 'swr';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { TemplateCard } from '@/components/templates/template-card';
import { TemplateEditDialog } from '@/components/templates/edit-dialog';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { confirm, prompt } from '@/hooks/use-one-dialog';
import { Template, TemplateField } from '@/components/templates/shared';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { DocumentUploadWithTemplate } from '@/components/jobs/document-upload-with-template';
import { useRouter } from 'next/navigation';

interface TemplatesData {
  public: Template[];
  user: Template[];
}

const fetcher = async (url: string): Promise<TemplatesData> => {
  const response = await fetch(url);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch templates');
  }

  return result.data;
};

export default function TemplatesClientPage() {
  const router = useRouter();
  const {
    data: templates,
    error,
    mutate,
    isLoading,
  } = useSWR('/api/templates', fetcher);

  const [searchQuery, setSearchQuery] = useState('');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [templateToUse, setTemplateToUse] = useState<Template | null>(null);
  const [, setCopyLoading] = useState(false);

  const handleUseTemplate = (template: Template) => {
    setTemplateToUse(template);
    setUploadDialogOpen(true);
  };

  const handleEditTemplate = async (templateData: {
    name: string;
    description?: string;
    definition: Record<string, TemplateField>;
  }) => {
    if (!editingTemplate) return;

    try {
      setEditLoading(true);
      const response = await fetch(`/api/templates/${editingTemplate.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to update template');
      }

      // Refresh templates list
      mutate();
      setEditDialogOpen(false);
      setEditingTemplate(null);
    } catch (err) {
      console.error('Error updating template:', err);
      // You might want to show a toast notification here
    } finally {
      setEditLoading(false);
    }
  };

  const handleDeleteTemplate = async (template: Template) => {
    if (
      !(await confirm({
        title: 'Delete Template',
        body: `Are you sure you want to delete "${template.name}"? This action cannot be undone.`,
        closeButton: 'Cancel',
        actionButton: 'Delete',
        actionButtonVariant: 'destructive',
      }))
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/templates/${template.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete template');
      }

      // Refresh templates list
      mutate();
    } catch (err) {
      console.error('Error deleting template:', err);
      // You might want to show a toast notification here
    }
  };

  const handleCopyTemplate = async (template: Template) => {
    const newName = await prompt({
      title: 'Copy Template',
      body: 'Enter the new template name',
      actionButton: 'Create',
      closeButton: 'Cancel',
      defaultValue: `Copy of ${template.name}`,
    });
    if (!newName) {
      return;
    }

    try {
      setCopyLoading(true);

      const copyData = {
        name: newName,
        description: template.description,
        definition: template.definition,
      };

      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(copyData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to copy template');
      }

      // Refresh templates list
      mutate();
    } catch (err) {
      console.error('Error copying template:', err);
      // You might want to show a toast notification here
    } finally {
      setCopyLoading(false);
    }
  };

  const filteredPublicTemplates =
    templates?.public.filter((template) =>
      template.name.toLowerCase().includes(searchQuery.toLowerCase()),
    ) || [];

  const filteredUserTemplates =
    templates?.user.filter((template) =>
      template.name.toLowerCase().includes(searchQuery.toLowerCase()),
    ) || [];

  if (isLoading && !templates) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-64" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-red-600">
          Failed to load templates
        </h2>
        <p className="text-gray-600 mt-2">
          {error.message ||
            'An unexpected error occurred. Please try again later.'}
        </p>
        <Button onClick={() => mutate()} className="mt-6">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search templates..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Public Templates Section */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Built-in Templates</h2>
        {filteredPublicTemplates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPublicTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onUse={handleUseTemplate}
                onCopy={handleCopyTemplate}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {searchQuery
              ? 'No built-in templates match your search.'
              : 'No built-in templates available.'}
          </div>
        )}
      </div>

      {/* User Templates Section */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Your Templates</h2>
        {filteredUserTemplates.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredUserTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onUse={handleUseTemplate}
                onEdit={(template) => {
                  setEditingTemplate(template);
                  setEditDialogOpen(true);
                }}
                onDelete={handleDeleteTemplate}
                onCopy={handleCopyTemplate}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            {searchQuery
              ? 'No custom templates match your search.'
              : "You haven't created any templates yet."}
          </div>
        )}
      </div>

      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Upload a Document</DialogTitle>
          </DialogHeader>
          <DocumentUploadWithTemplate
            template={templateToUse}
            onComplete={() => {
              setUploadDialogOpen(false);
              router.push('/dashboard/jobs');
            }}
            onCancel={() => setUploadDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      <TemplateEditDialog
        open={editDialogOpen}
        onOpenChange={(open) => {
          setEditDialogOpen(open);
          if (!open) {
            setEditingTemplate(null);
          }
        }}
        template={editingTemplate}
        onSubmit={handleEditTemplate}
        loading={editLoading}
      />
    </div>
  );
}
