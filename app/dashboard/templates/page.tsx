import { Metadata } from 'next';
import TemplatesClientPage from './client-page';
import { DashboardWrapper } from '@/components/dashboard/wrapper';
import { TemplateCreateButton } from '@/components/templates/create-button';
import { requireUser } from '@/lib/auth';
import { templateOperations } from '@/lib/db';
import { SWRConfig } from 'swr';

export const metadata: Metadata = {
  title: 'Templates',
};

export default async function TemplatesPage() {
  const user = await requireUser();
  const [publicTemplates, userTemplates] = await Promise.all([
    templateOperations.findPublic(),
    templateOperations.findByUser(user.id),
  ]);

  return (
    <DashboardWrapper
      breadcrumb={[{ title: 'Templates', href: '/dashboard/templates' }]}
      className="p-6"
    >
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Templates</h1>
          <p className="text-gray-600 mt-1">
            Manage data extraction templates for your documents
          </p>
        </div>
        <TemplateCreateButton />
      </div>
      <SWRConfig
        value={{
          fallback: {
            '/api/templates': {
              public: publicTemplates,
              user: userTemplates,
            },
          },
        }}
      >
        <TemplatesClientPage />
      </SWRConfig>
    </DashboardWrapper>
  );
}
