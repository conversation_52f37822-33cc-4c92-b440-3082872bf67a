'use client';

import * as React from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { confirm } from '@/hooks/use-one-dialog';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WebhookEndpoint } from '@prisma/client';
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Controller } from 'react-hook-form';
import { Label } from '@/components/ui/label';
import { Pencil, Trash2, CheckCircle, XCircle, RefreshCcw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

async function getWebhooks(): Promise<WebhookEndpoint[]> {
  const res = await fetch('/api/webhooks');
  if (!res.ok) {
    throw new Error('Failed to fetch webhooks');
  }
  return res.json();
}

async function deleteWebhook(id: string) {
  const res = await fetch(`/api/webhooks/${id}`, {
    method: 'DELETE',
  });
  if (!res.ok) {
    throw new Error('Failed to delete webhook');
  }
}

const eventOptions = [
  { value: 'job.completed', label: 'job.completed' },
  { value: 'job.failed', label: 'job.failed' },
];

const editWebhookSchema = z.object({
  url: z.string().url('Enter a valid URL'),
  events: z.array(z.string()).min(1, 'Select at least one event'),
  secret: z.string().min(1, 'Secret is required'),
  enabled: z.boolean(),
});

async function updateWebhookAllFields(
  id: string,
  data: z.infer<typeof editWebhookSchema>
) {
  const res = await fetch(`/api/webhooks/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  if (!res.ok) {
    throw new Error('Failed to update webhook');
  }
  return res.json();
}

export function WebhooksClientPage() {
  const queryClient = useQueryClient();
  const { data: webhooks, isLoading } = useQuery({
    queryKey: ['webhooks'],
    queryFn: getWebhooks,
  });
  const deleteMutation = useMutation({
    mutationFn: deleteWebhook,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['webhooks'] });
      toast.success('Webhook deleted successfully');
    },
    onError: () => {
      toast.error('Failed to delete webhook');
    },
  });

  // Edit modal state
  const [editWebhook, setEditWebhook] = React.useState<WebhookEndpoint | null>(
    null
  );
  const editForm = useForm<z.infer<typeof editWebhookSchema>>({
    resolver: zodResolver(editWebhookSchema),
    defaultValues: { url: '', events: [], secret: '', enabled: true },
  });
  const editMutation = useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: z.infer<typeof editWebhookSchema>;
    }) => updateWebhookAllFields(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['webhooks'] });
      toast.success('Webhook updated successfully');
      setEditWebhook(null);
    },
    onError: () => {
      toast.error('Failed to update webhook');
    },
  });

  React.useEffect(() => {
    if (editWebhook) {
      editForm.reset({
        url: editWebhook.url,
        events: editWebhook.events,
        secret: editWebhook.secret || '',
        enabled: editWebhook.enabled,
      });
    }
  }, [editWebhook]);

  return (
    <Card>
      <CardContent>
        {isLoading ? (
          <p>Loading...</p>
        ) : !webhooks || webhooks.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <p className="mb-4 text-gray-500">No webhooks yet.</p>
            {/* Optionally, add a create button here if available */}
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>URL</TableHead>
                <TableHead>Events</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {webhooks.map((webhook) => (
                <TableRow key={webhook.id}>
                  <TableCell>{webhook.url}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {webhook.events.map((event) => (
                        <Badge key={event}>{event}</Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    {webhook.enabled ? (
                      <span className="flex items-center gap-1 text-green-600">
                        <CheckCircle className="w-4 h-4" /> Enabled
                      </span>
                    ) : (
                      <span className="flex items-center gap-1 text-red-500">
                        <XCircle className="w-4 h-4" /> Disabled
                      </span>
                    )}
                  </TableCell>
                  <TableCell className="flex gap-2 justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditWebhook(webhook)}
                    >
                      <Pencil className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={async () => {
                        const confirmed = await confirm({
                          title: 'Delete Webhook',
                          body: 'Are you sure you want to delete this webhook? This action cannot be undone.',
                          actionButtonVariant: 'destructive',
                        });
                        if (confirmed) {
                          deleteMutation.mutate(webhook.id);
                        }
                      }}
                      disabled={deleteMutation.isPending}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
        <Dialog
          open={!!editWebhook}
          onOpenChange={(open) => {
            if (!open) setEditWebhook(null);
          }}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Webhook</DialogTitle>
              <DialogDescription>
                Edit the webhook details below.
              </DialogDescription>
            </DialogHeader>
            <Form {...editForm}>
              <form
                onSubmit={editForm.handleSubmit((values) => {
                  if (editWebhook) {
                    editMutation.mutate({ id: editWebhook.id, data: values });
                  }
                })}
                className="space-y-4"
              >
                <FormField
                  control={editForm.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>URL</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="https://example.com/webhook"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="secret"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Secret</FormLabel>
                      <div className="flex space-x-2">
                        <FormControl>
                          <Input placeholder="your-secret-token" {...field} />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            const newSecret =
                              Math.random().toString(36).substring(2, 15) +
                              Math.random().toString(36).substring(2, 15);
                            editForm.setValue('secret', newSecret);
                          }}
                        >
                          <RefreshCcw className="w-4 h-4 mr-1" /> Generate
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="events"
                  render={() => (
                    <FormItem>
                      <FormLabel>Events</FormLabel>
                      <FormControl>
                        <div className="flex flex-col gap-2">
                          {eventOptions.map((opt) => (
                            <label
                              key={opt.value}
                              className="flex items-center gap-2"
                            >
                              <Checkbox
                                checked={editForm
                                  .watch('events')
                                  .includes(opt.value)}
                                onCheckedChange={(checked) => {
                                  const current = editForm.getValues('events');
                                  if (checked) {
                                    editForm.setValue('events', [
                                      ...current,
                                      opt.value,
                                    ]);
                                  } else {
                                    editForm.setValue(
                                      'events',
                                      current.filter((v) => v !== opt.value)
                                    );
                                  }
                                }}
                              />
                              <span>{opt.label}</span>
                            </label>
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Controller
                  name="enabled"
                  control={editForm.control}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Enabled</FormLabel>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="edit-enabled-switch"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <Label htmlFor="edit-enabled-switch">Enabled</Label>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit" disabled={editMutation.isPending}>
                    {editMutation.isPending ? 'Saving...' : 'Save'}
                  </Button>
                  <DialogClose asChild>
                    <Button type="button" variant="outline">
                      Cancel
                    </Button>
                  </DialogClose>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
