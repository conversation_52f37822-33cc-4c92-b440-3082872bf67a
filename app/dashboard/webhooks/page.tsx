import * as React from 'react';
import { Metadata } from 'next';

import {
  PageHeader,
  PageHeaderDescription,
  PageHeaderHeading,
} from '@/components/page-header';
import { DashboardWrapper } from '@/components/dashboard/wrapper';
import { WebhooksClientPage } from './client-page';
import { CreateWebhookDialog } from '@/components/webhooks/create-dialog';

export const metadata: Metadata = {
  title: 'Webhooks',
};

export default function WebhooksPage() {
  return (
    <DashboardWrapper
      breadcrumb={[{ title: 'Webhooks', href: '/dashboard/webhooks' }]}
      className="p-6"
    >
      <PageHeader actions={<CreateWebhookDialog />}>
        <PageHeaderHeading>Webhooks</PageHeaderHeading>
        <PageHeaderDescription>
          Webhooks allow you to receive notifications about events that happen
          in your account.
        </PageHeaderDescription>
      </PageHeader>
      <WebhooksClientPage />
    </DashboardWrapper>
  );
}
