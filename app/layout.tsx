import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';
import { Analytics } from '@vercel/analytics/react';
import './globals.css';
import { OneDialog } from '@/components/ui/one-dialog';
import { Toaster } from 'sonner';
import { ScreenSize } from '@/components/ui/screen-size';
import { SWRConfig } from 'swr';
import { getCurrentUser } from '@/lib/auth';
import { UserProvider } from '@/contexts/user-context';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    template: '%s | Unfurl',
    default: 'Unfurl - Intelligent Data Extraction, Simplified.',
  },
  description:
    'Unfurl is the AI-powered platform that ends manual data entry. Extract structured data from invoices, receipts, and forms with incredible accuracy.',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const user = await getCurrentUser();

  return (
    <SWRConfig
      value={{
        fallback: { '/api/user': user },
      }}
    >
      <UserProvider>
        <html lang="en">
          <body className={inter.className}>
            {children}
            <OneDialog />
            <Toaster />
            {process.env.APP_ENV === 'development' && <ScreenSize />}
            <Analytics />
          </body>
        </html>
      </UserProvider>
    </SWRConfig>
  );
}
