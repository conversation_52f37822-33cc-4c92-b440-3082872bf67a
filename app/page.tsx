import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Download,
  Zap,
  Check,
  Shield,
  Code,
  Webhook,
  Terminal,
  Building2,
  TrendingUp,
  Scale,
  FileSpreadsheet,
  Users,
  Star,
} from 'lucide-react';
import { GetStartedButton } from '@/components/web/get-started-button';
import { WebHeader } from '@/components/web/header';
import { WebFooter } from '@/components/web/footer';
import Link from 'next/link';

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-white">
      <WebHeader />
      {/* Hero Section */}
      <header className="pt-20 pb-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center max-w-4xl mx-auto">
            <Badge
              className="mb-6 bg-blue-50 text-primary border-blue-200 hover:bg-blue-100"
              aria-label="Product announcement"
            >
              🚀 Now with 99.5% accuracy on complex documents
            </Badge>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              Extract Data from Any Document —{' '}
              <span className="text-primary">Automatically</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Unfurl uses AI to transform PDFs, images, and emails into
              structured data — fast, accurate, and scalable.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <GetStartedButton />
              <Button
                asChild
                variant="ghost"
                size="lg"
                className="px-8 py-4 text-lg rounded-lg"
                aria-label="Explore Unfurl API documentation"
              >
                <Link href="/api-docs">
                  Explore the API
                  <Code className="ml-2 w-5 h-5" aria-hidden="true" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
        <div className="mt-12">
          <div className="relative mx-auto max-w-lg animate-fade-in">
            {/* Placeholder for demo visualization */}
            <div className="bg-white rounded-2xl shadow-2xl p-8 border border-gray-200">
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white rounded"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded w-32"></div>
                </div>

                <div className="space-y-2">
                  <div className="h-3 bg-gray-100 rounded w-full"></div>
                  <div className="h-3 bg-gray-100 rounded w-4/5"></div>
                  <div className="h-3 bg-gray-100 rounded w-3/5"></div>
                </div>

                <div className="border-t pt-4">
                  <div className="text-xs text-gray-500 mb-2">
                    Extracted Data
                  </div>
                  <div className="bg-primary-50 rounded-lg p-3 text-sm font-mono">
                    {`{
          "invoice_number": "INV-2024-001",
          "amount": "$1,249.99",
          "date": "2024-01-15"
        }`}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Key Features */}
      <section
        id="features"
        className="py-16 bg-gray-50"
        aria-labelledby="features-heading"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2
              id="features-heading"
              className="text-3xl font-bold text-gray-900 mb-4"
            >
              Powerful AI-Driven Features
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to automate your data extraction workflows
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow rounded-lg p-6">
              <header className="text-center">
                <div
                  className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4"
                  aria-hidden="true"
                >
                  <FileText className="w-6 h-6 text-white" aria-hidden="true" />
                </div>
                <h3 className="text-xl font-semibold mb-4">
                  OCR for PDFs & Images
                </h3>
              </header>
              <p className="text-gray-600 text-center">
                Advanced optical character recognition that works with complex
                layouts, handwriting, and low-quality scans.
              </p>
            </article>
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow rounded-lg p-6">
              <header className="text-center">
                <div
                  className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4"
                  aria-hidden="true"
                >
                  <Download className="w-6 h-6 text-white" aria-hidden="true" />
                </div>
                <h3 className="text-xl font-semibold mb-4">JSON/CSV Export</h3>
              </header>
              <p className="text-gray-600 text-center">
                Export your extracted data in multiple formats including JSON,
                CSV, and XML for seamless integration.
              </p>
            </article>
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow rounded-lg p-6">
              <header className="text-center">
                <div
                  className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center mx-auto mb-4"
                  aria-hidden="true"
                >
                  <Zap className="w-6 h-6 text-white" aria-hidden="true" />
                </div>
                <h3 className="text-xl font-semibold mb-4">
                  API & Zapier Integration
                </h3>
              </header>
              <p className="text-gray-600 text-center">
                Connect with 5,000+ apps through Zapier or use our REST API for
                custom integrations.
              </p>
            </article>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16" aria-labelledby="how-it-works-heading">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2
              id="how-it-works-heading"
              className="text-3xl font-bold text-gray-900 mb-4"
            >
              How It Works
            </h2>
            <p className="text-lg text-gray-600">
              Three simple steps to transform your documents into structured
              data
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <article className="text-center">
              <h3 className="text-xl font-semibold mb-4">Step 1: Upload</h3>
              <p className="text-gray-600">
                Upload your PDFs, images, emails, or paste URLs. We support 50+
                file formats.
              </p>
            </article>
            <article className="text-center">
              <h3 className="text-xl font-semibold mb-4">Step 2: Extract</h3>
              <p className="text-gray-600">
                Our AI analyzes and extracts structured data using advanced OCR,
                NLP, and machine learning.
              </p>
            </article>
            <article className="text-center">
              <h3 className="text-xl font-semibold mb-4">Step 3: Export</h3>
              <p className="text-gray-600">
                Download your data as JSON, CSV, or integrate directly with your
                existing systems via API.
              </p>
            </article>
          </div>
        </div>
      </section>

      {/* Use Cases */}
      <section className="py-16 bg-gray-50" aria-labelledby="use-cases-heading">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2
              id="use-cases-heading"
              className="text-3xl font-bold text-gray-900 mb-4"
            >
              Trusted Across Industries
            </h2>
            <p className="text-lg text-gray-600">
              From startups to enterprise, teams use Unfurl to automate their
              data workflows
            </p>
          </div>
          <div className="grid md:grid-cols-5 gap-6">
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow text-center rounded-lg p-6">
              <TrendingUp
                className="w-8 h-8 text-primary mx-auto mb-3"
                aria-hidden="true"
              />
              <h3 className="font-semibold mb-2">Finance</h3>
              <p className="text-sm text-gray-600">
                Invoice processing, expense reports, financial statements
              </p>
            </article>
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow text-center rounded-lg p-6">
              <FileSpreadsheet
                className="w-8 h-8 text-primary mx-auto mb-3"
                aria-hidden="true"
              />
              <h3 className="font-semibold mb-2">Logistics</h3>
              <p className="text-sm text-gray-600">
                Shipping labels, customs forms, delivery receipts
              </p>
            </article>
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow text-center rounded-lg p-6">
              <Scale
                className="w-8 h-8 text-primary mx-auto mb-3"
                aria-hidden="true"
              />
              <h3 className="font-semibold mb-2">Legal</h3>
              <p className="text-sm text-gray-600">
                Contract analysis, legal document review, compliance
              </p>
            </article>
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow text-center rounded-lg p-6">
              <Building2
                className="w-8 h-8 text-primary mx-auto mb-3"
                aria-hidden="true"
              />
              <h3 className="font-semibold mb-2">SaaS</h3>
              <p className="text-sm text-gray-600">
                User onboarding, document verification, data migration
              </p>
            </article>
            <article className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow text-center rounded-lg p-6">
              <Users
                className="w-8 h-8 text-primary mx-auto mb-3"
                aria-hidden="true"
              />
              <h3 className="font-semibold mb-2">Research</h3>
              <p className="text-sm text-gray-600">
                Academic papers, survey data, research documentation
              </p>
            </article>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-16" aria-labelledby="testimonials-heading">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2
              id="testimonials-heading"
              className="text-3xl font-bold text-gray-900 mb-4"
            >
              Loved by Teams Worldwide
            </h2>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <article className="bg-white border-0 shadow-sm rounded-lg p-6">
              <div className="flex mb-4" aria-label="5 star rating">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-current"
                    aria-hidden="true"
                  />
                ))}
              </div>
              <blockquote className="text-gray-600 mb-4">
                "Unfurl reduced our invoice processing time from hours to
                minutes. The accuracy is incredible."
              </blockquote>
              <footer className="flex items-center">
                <div
                  className="w-10 h-10 bg-gray-200 rounded-full mr-3"
                  aria-hidden="true"
                ></div>
                <div>
                  <cite className="font-semibold not-italic">Sarah Chen</cite>
                  <p className="text-sm text-gray-500">
                    Finance Director, TechCorp
                  </p>
                </div>
              </footer>
            </article>
            <article className="bg-white border-0 shadow-sm rounded-lg p-6">
              <div className="flex mb-4" aria-label="5 star rating">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-current"
                    aria-hidden="true"
                  />
                ))}
              </div>
              <blockquote className="text-gray-600 mb-4">
                "The API integration was seamless. We're now processing 10x more
                documents with the same team."
              </blockquote>
              <footer className="flex items-center">
                <div
                  className="w-10 h-10 bg-gray-200 rounded-full mr-3"
                  aria-hidden="true"
                ></div>
                <div>
                  <cite className="font-semibold not-italic">
                    Marcus Rodriguez
                  </cite>
                  <p className="text-sm text-gray-500">CTO, DataFlow Inc</p>
                </div>
              </footer>
            </article>
            <article className="bg-white border-0 shadow-sm rounded-lg p-6">
              <div className="flex mb-4" aria-label="5 star rating">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="w-5 h-5 text-yellow-400 fill-current"
                    aria-hidden="true"
                  />
                ))}
              </div>
              <blockquote className="text-gray-600 mb-4">
                "Game-changer for our legal document review process. Highly
                recommend for any data-heavy workflow."
              </blockquote>
              <footer className="flex items-center">
                <div
                  className="w-10 h-10 bg-gray-200 rounded-full mr-3"
                  aria-hidden="true"
                ></div>
                <div>
                  <cite className="font-semibold not-italic">Emily Watson</cite>
                  <p className="text-sm text-gray-500">
                    Operations Lead, LegalTech
                  </p>
                </div>
              </footer>
            </article>
          </div>
        </div>
      </section>

      

      {/* Security & Developer Ready */}
      <section className="py-16" aria-labelledby="security-developer-heading">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12">
            <article>
              <h2
                id="security-developer-heading"
                className="text-3xl font-bold text-gray-900 mb-6"
              >
                Enterprise-Grade Security
              </h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <Shield
                    className="w-6 h-6 text-primary mr-3 mt-1"
                    aria-hidden="true"
                  />
                  <div>
                    <h3 className="font-semibold mb-1">GDPR Compliant</h3>
                    <p className="text-gray-600">
                      Full compliance with European data protection regulations
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Shield
                    className="w-6 h-6 text-primary mr-3 mt-1"
                    aria-hidden="true"
                  />
                  <div>
                    <h3 className="font-semibold mb-1">SOC2 Ready</h3>
                    <p className="text-gray-600">
                      Meeting the highest standards for security and
                      availability
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Shield
                    className="w-6 h-6 text-primary mr-3 mt-1"
                    aria-hidden="true"
                  />
                  <div>
                    <h3 className="font-semibold mb-1">
                      End-to-End Encryption
                    </h3>
                    <p className="text-gray-600">
                      Your data is encrypted in transit and at rest
                    </p>
                  </div>
                </div>
              </div>
            </article>
            <article>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Developer Ready
              </h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <Code
                    className="w-6 h-6 text-primary mr-3 mt-1"
                    aria-hidden="true"
                  />
                  <div>
                    <h3 className="font-semibold mb-1">API-First Design</h3>
                    <p className="text-gray-600">
                      RESTful API with comprehensive documentation and SDKs
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Terminal
                    className="w-6 h-6 text-primary mr-3 mt-1"
                    aria-hidden="true"
                  />
                  <div>
                    <h3 className="font-semibold mb-1">CLI Tools</h3>
                    <p className="text-gray-600">
                      Command-line interface for batch processing and automation
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Webhook
                    className="w-6 h-6 text-primary mr-3 mt-1"
                    aria-hidden="true"
                  />
                  <div>
                    <h3 className="font-semibold mb-1">Webhooks</h3>
                    <p className="text-gray-600">
                      Real-time notifications when your documents are processed
                    </p>
                  </div>
                </div>
              </div>
            </article>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary" aria-labelledby="cta-heading">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2
            id="cta-heading"
            className="text-3xl md:text-4xl font-bold text-white mb-4"
          >
            Ready to Transform Your Data Workflows?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Join thousands of teams already using Unfurl to automate their
            document processing
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <GetStartedButton className="bg-white text-primary hover:bg-gray-100 px-8 py-3 text-lg" />
            {/* <Button
              variant="outline"
              size="lg"
              className="border-white text-white hover:bg-white hover:text-primary px-8 py-3 text-lg bg-transparent"
              aria-label="Schedule a product demo"
            >
              Schedule Demo
            </Button> */}
          </div>
        </div>
      </section>

      <WebFooter />
    </div>
  );
}
