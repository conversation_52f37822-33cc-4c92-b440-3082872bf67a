import { WebHeader } from '@/components/web/header';
import { WebFooter } from '@/components/web/footer';
import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Pricing',
};

export default function PricingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <WebHeader />
      <main className="flex-1">
        <section
          id="pricing"
          className="py-16 bg-gray-50"
          aria-labelledby="pricing-heading"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h1
                id="pricing-heading"
                className="text-3xl font-bold text-gray-900 mb-4"
              >
                Simple, Transparent Pricing
              </h1>
              <p className="text-lg text-gray-600">
                Start free, scale as you grow
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              <article className="bg-white border-2 border-gray-200 relative rounded-lg">
                <header className="text-center p-6">
                  <h2 className="text-2xl font-semibold">Free</h2>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    $0
                    <span className="text-base font-normal text-gray-500">
                      /month
                    </span>
                  </p>
                </header>
                <div className="p-6 pt-0">
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      20 credits/month
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Basic OCR & NLP
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      JSON/CSV export
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Email support
                    </li>
                  </ul>
                  <Button
                    className="w-full bg-transparent"
                    variant="outline"
                    aria-label="Get started with free plan"
                  >
                    Get Started
                  </Button>
                </div>
              </article>
              <article className="bg-white border-2 border-primary relative rounded-lg">
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-white">
                  Most Popular
                </Badge>
                <header className="text-center p-6">
                  <h2 className="text-2xl font-semibold">Pro</h2>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    $29
                    <span className="text-base font-normal text-gray-500">
                      /month
                    </span>
                  </p>
                </header>
                <div className="p-6 pt-0">
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      1,000 credits/month
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Advanced AI models
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      API access
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Zapier integration
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Priority support
                    </li>
                  </ul>
                  <Button
                    className="w-full bg-primary hover:bg-primary/90"
                    aria-label="Start Pro plan trial"
                  >
                    Start Pro Trial
                  </Button>
                </div>
              </article>
              <article className="bg-white border-2 border-gray-200 relative rounded-lg">
                <header className="text-center p-6">
                  <h2 className="text-2xl font-semibold">Enterprise</h2>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    Custom
                  </p>
                </header>
                <div className="p-6 pt-0">
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Unlimited credits
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Custom models
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      On-premise deployment
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      SLA & dedicated support
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Custom integrations
                    </li>
                  </ul>
                  <Button
                    className="w-full bg-transparent"
                    variant="outline"
                    aria-label="Contact sales for enterprise plan"
                  >
                    Contact Sales
                  </Button>
                </div>
              </article>
            </div>
          </div>
        </section>
      </main>
      <WebFooter />
    </div>
  );
}
