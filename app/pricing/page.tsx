import { WebHeader } from '@/components/web/header';
import { WebFooter } from '@/components/web/footer';
import { Metadata } from 'next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

export const metadata: Metadata = {
  title: 'Simple Credit-Based Pricing - Start Free',
  description:
    'Transparent pricing based on document processing volume. Start with 10 free credits, then pay as you scale.',
};

export default function PricingPage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <WebHeader />
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-16 bg-gradient-to-br from-blue-50 to-indigo-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Simple Credit-Based Pricing
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                Pay only for what you process. 1 credit = 1 page processed.
              </p>
              <div className="inline-flex items-center bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium">
                ✨ Start with 10 free credits every month
              </div>
            </div>

            {/* Billing Toggle */}
            <div className="flex justify-center mb-8">
              <Tabs defaultValue="monthly" className="w-auto">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="monthly">Monthly</TabsTrigger>
                  <TabsTrigger value="annually">
                    Annually
                    <Badge className="ml-2 bg-green-500 text-white text-xs">
                      Save 20%
                    </Badge>
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section
          id="pricing"
          className="py-16 bg-gray-50"
          aria-labelledby="pricing-heading"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Free Plan */}
              <article className="bg-white border-2 border-gray-200 relative rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <header className="text-center p-6">
                  <h2 className="text-2xl font-semibold">Free</h2>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    $0
                    <span className="text-base font-normal text-gray-500">
                      /month
                    </span>
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Perfect for trying out
                  </p>
                </header>
                <div className="p-6 pt-0">
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      10 credits/month
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Basic OCR & data extraction
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      JSON/CSV export
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Email support
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      30-day data retention
                    </li>
                  </ul>
                  <Button
                    className="w-full bg-transparent"
                    variant="outline"
                    aria-label="Get started with free plan"
                  >
                    Get Started Free
                  </Button>
                </div>
              </article>

              {/* Pro Plan */}
              <article className="bg-white border-2 border-blue-500 relative rounded-lg shadow-lg hover:shadow-xl transition-shadow">
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white">
                  Most Popular
                </Badge>
                <header className="text-center p-6">
                  <h2 className="text-2xl font-semibold">Pro</h2>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    $29
                    <span className="text-base font-normal text-gray-500">
                      /month
                    </span>
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    $0.029 per credit
                  </p>
                </header>
                <div className="p-6 pt-0">
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      1,000 credits/month
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Advanced AI models (Mistral OCR)
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Template schema builder
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      API access
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Webhook integrations
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Priority support
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      1-year data retention
                    </li>
                  </ul>
                  <Button
                    className="w-full bg-blue-500 hover:bg-blue-600 text-white"
                    aria-label="Start Pro plan trial"
                  >
                    Start Pro Trial
                  </Button>
                </div>
              </article>

              {/* Enterprise Plan */}
              <article className="bg-white border-2 border-gray-200 relative rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <header className="text-center p-6">
                  <h2 className="text-2xl font-semibold">Enterprise</h2>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    Custom
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    Volume discounts available
                  </p>
                </header>
                <div className="p-6 pt-0">
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Custom credit packages
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Custom AI models
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Multi-user accounts
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      On-premise deployment
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      SLA & dedicated support
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Custom integrations
                    </li>
                    <li className="flex items-center">
                      <Check
                        className="w-5 h-5 text-green-500 mr-2"
                        aria-hidden="true"
                      />
                      Unlimited data retention
                    </li>
                  </ul>
                  <Button
                    className="w-full bg-transparent"
                    variant="outline"
                    aria-label="Contact sales for enterprise plan"
                  >
                    Contact Sales
                  </Button>
                </div>
              </article>
            </div>
          </div>
        </section>

        {/* Feature Comparison Table */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Feature Comparison
              </h2>
              <p className="text-lg text-gray-600">
                Compare all features across our plans
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse bg-white rounded-lg shadow-sm">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="text-left p-4 font-semibold text-gray-900 border-b">
                      Feature
                    </th>
                    <th className="text-center p-4 font-semibold text-gray-900 border-b">
                      Free
                    </th>
                    <th className="text-center p-4 font-semibold text-gray-900 border-b bg-blue-50">
                      Pro
                    </th>
                    <th className="text-center p-4 font-semibold text-gray-900 border-b">
                      Enterprise
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4 font-medium">Credits per month</td>
                    <td className="p-4 text-center">10</td>
                    <td className="p-4 text-center bg-blue-50">1,000</td>
                    <td className="p-4 text-center">Custom</td>
                  </tr>
                  <tr className="border-b bg-gray-50">
                    <td className="p-4 font-medium">OCR & Data Extraction</td>
                    <td className="p-4 text-center">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                    <td className="p-4 text-center bg-blue-50">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                    <td className="p-4 text-center">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-medium">Mistral OCR AI Models</td>
                    <td className="p-4 text-center">Basic</td>
                    <td className="p-4 text-center bg-blue-50">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                    <td className="p-4 text-center">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                  </tr>
                  <tr className="border-b bg-gray-50">
                    <td className="p-4 font-medium">Template Schema Builder</td>
                    <td className="p-4 text-center">-</td>
                    <td className="p-4 text-center bg-blue-50">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                    <td className="p-4 text-center">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-medium">API Access</td>
                    <td className="p-4 text-center">-</td>
                    <td className="p-4 text-center bg-blue-50">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                    <td className="p-4 text-center">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                  </tr>
                  <tr className="border-b bg-gray-50">
                    <td className="p-4 font-medium">Webhook Integrations</td>
                    <td className="p-4 text-center">-</td>
                    <td className="p-4 text-center bg-blue-50">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                    <td className="p-4 text-center">
                      <Check className="w-5 h-5 text-green-500 mx-auto" />
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-medium">Multi-user Accounts</td>
                    <td className="p-4 text-center">1 user</td>
                    <td className="p-4 text-center bg-blue-50">1 user</td>
                    <td className="p-4 text-center">Unlimited</td>
                  </tr>
                  <tr className="border-b bg-gray-50">
                    <td className="p-4 font-medium">Data Retention</td>
                    <td className="p-4 text-center">30 days</td>
                    <td className="p-4 text-center bg-blue-50">1 year</td>
                    <td className="p-4 text-center">Unlimited</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4 font-medium">Support</td>
                    <td className="p-4 text-center">Email</td>
                    <td className="p-4 text-center bg-blue-50">Priority</td>
                    <td className="p-4 text-center">Dedicated</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-gray-50">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-lg text-gray-600">
                Can&apos;t find the answer you&apos;re looking for?{' '}
                <a
                  href="/contact"
                  className="text-blue-600 hover:text-blue-800"
                >
                  Contact us
                </a>{' '}
                today.
              </p>
            </div>

            <div className="space-y-8">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  What is a credit?
                </h3>
                <p className="text-gray-600">
                  Our pricing is based on credits, where{' '}
                  <strong>1 credit equals 1 page processed</strong>. This
                  includes both document processing (OCR) and data extraction.
                  Unused credits expire at the end of each billing period.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  What counts as a page?
                </h3>
                <p className="text-gray-600 mb-3">
                  For PDFs, each page in the document counts as 1 credit. For
                  other document types:
                </p>
                <ul className="text-gray-600 space-y-1 ml-4">
                  <li>• Processing an email costs 1 credit</li>
                  <li>• Processing a single-page PDF costs 1 credit</li>
                  <li>• Processing a three-page PDF costs 3 credits</li>
                  <li>• Processing an image document costs 1 credit</li>
                </ul>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  How does the Mistral OCR work?
                </h3>
                <p className="text-gray-600">
                  We use Mistral AI's latest OCR models for document processing.
                  You can create custom templates to define exactly what data
                  you want to extract, and our AI will automatically identify
                  and extract those fields from your documents with high
                  accuracy.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Can I change my subscription?
                </h3>
                <p className="text-gray-600">
                  Yes, you can upgrade or downgrade your subscription at any
                  time. When upgrading, the change is immediate and unused
                  credits are added to your new quota. When downgrading, the
                  change happens at the next billing cycle.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  What integrations are available?
                </h3>
                <p className="text-gray-600">
                  We support JSON/CSV exports, webhook integrations, and API
                  access. You can easily integrate with your existing workflows
                  and send extracted data to your preferred applications and
                  databases.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Is there a free trial?
                </h3>
                <p className="text-gray-600">
                  Yes! Our free plan includes 10 credits every month, so you can
                  try our service without any commitment. No credit card
                  required to get started.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-blue-600">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to automate your document processing?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Start with 10 free credits. No credit card required.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100"
              >
                Start Free Trial
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                View Demo
              </Button>
            </div>
          </div>
        </section>
      </main>
      <WebFooter />
    </div>
  );
}
