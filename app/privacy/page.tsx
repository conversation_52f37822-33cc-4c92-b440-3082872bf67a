import { <PERSON>Header } from '@/components/web/header';
import { WebFooter } from '@/components/web/footer';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy',
};

export default function PrivacyPage() {
  return (
    <div className="min-h-screen flex flex-col bg-white">
      <WebHeader />
      <main className="flex-1 max-w-2xl mx-auto px-4 py-20">
        <h1 className="text-3xl font-bold mb-6 text-gray-900">
          Privacy Policy
        </h1>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Introduction</h2>
          <p className="text-gray-700 mb-4">
            Unfurl (&quot;we&quot;, &quot;us&quot;, or &quot;our&quot;) is
            committed to protecting your privacy. This Privacy Policy explains
            how we collect, use, disclose, and safeguard your information when
            you use our website and services.
          </p>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Information We Collect</h2>
          <ul className="list-disc pl-6 text-gray-700 mb-4">
            <li>
              <strong>Personal Information:</strong> such as your name, email
              address, and account details when you register or contact us.
            </li>
            <li>
              <strong>Usage Data:</strong> information about how you use our
              website and services, including IP address, browser type, and
              device information.
            </li>
            <li>
              <strong>Documents & Files:</strong> files you upload for
              processing, which are handled securely and deleted after
              processing unless you choose to store them.
            </li>
          </ul>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">How We Use Information</h2>
          <ul className="list-disc pl-6 text-gray-700 mb-4">
            <li>To provide, operate, and maintain our services</li>
            <li>To improve, personalize, and expand our services</li>
            <li>To communicate with you, including customer support</li>
            <li>To process your transactions and manage your account</li>
            <li>To comply with legal obligations</li>
          </ul>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Data Security</h2>
          <p className="text-gray-700 mb-4">
            We implement industry-standard security measures to protect your
            information. Data is encrypted in transit and at rest. Access to
            your data is restricted to authorized personnel only.
          </p>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Third-Party Services</h2>
          <p className="text-gray-700 mb-4">
            We may use third-party providers to help deliver our services (such
            as cloud storage and analytics). These providers are contractually
            obligated to protect your information and use it only for the
            purposes we specify.
          </p>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Your Rights</h2>
          <ul className="list-disc pl-6 text-gray-700 mb-4">
            <li>
              You may access, update, or delete your personal information at any
              time by contacting us.
            </li>
            <li>You may opt out of marketing communications at any time.</li>
            <li>
              If you are in the EU, you have additional rights under the GDPR.
            </li>
          </ul>
        </section>
        <section className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Contact Us</h2>
          <p className="text-gray-700 mb-4">
            If you have any questions or concerns about this Privacy Policy or
            our data practices, please contact us at{' '}
            <a
              href="mailto:<EMAIL>"
              className="text-primary underline"
            >
              <EMAIL>
            </a>
            .
          </p>
        </section>
        <section>
          <p className="text-gray-500 text-sm">
            Last updated: {new Date().toLocaleDateString()}
          </p>
        </section>
      </main>
      <WebFooter />
    </div>
  );
}
