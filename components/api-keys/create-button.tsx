'use client';

import { create<PERSON><PERSON><PERSON><PERSON> } from '@/actions/api-keys';
import { useState } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle, CheckCircle, Copy, Plus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { mutate } from 'swr';

interface NewAPIKey {
  id: string;
  name: string;
  createdAt: string;
  lastUsedAt: string | null;
  keyPreview: string;
  key: string;
  message: string;
}

export function APIKeyCreateButton() {
  const [creating, setCreating] = useState(false);

  // Dialog states
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showNewKeyDialog, setShowNewKeyDialog] = useState(false);

  // Form state
  const [newKeyName, setNewKeyName] = useState('');
  const [newAPIKey, setNewAPIKey] = useState<NewAPIKey | null>(null);
  const [copied, setCopied] = useState(false);

  // Create new API key
  const handleCreateAPIKey = async () => {
    if (!newKeyName.trim()) {
      toast.error('Please enter a name for your API key');
      return;
    }

    setCreating(true);
    try {
      const result = await createAPIKey(newKeyName.trim());

      if (result.success && result.data) {
        setNewAPIKey(result.data);
        setShowCreateDialog(false);
        setShowNewKeyDialog(true);
        setNewKeyName('');

        // Refresh the list
        mutate('/api/api-keys');

        toast.success('API key created successfully');
      } else {
        throw new Error(result.error || 'Failed to create API key');
      }
    } catch (error) {
      console.error('Error creating API key:', error);
      toast.error(
        error instanceof Error ? error.message : 'Failed to create API key',
      );
    } finally {
      setCreating(false);
    }
  };

  // Copy to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      toast.success('API key copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error('Failed to copy to clipboard');
    }
  };

  return (
    <>
      <Button onClick={() => setShowCreateDialog(true)}>
        <Plus className="w-4 h-4 mr-2" />
        Create API Key
      </Button>

      {/* Create API Key Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New API Key</DialogTitle>
            <DialogDescription>
              Give your API key a descriptive name to help you identify its
              purpose.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <Input
              placeholder="e.g., Production Integration, Development Testing"
              value={newKeyName}
              onChange={(e) => setNewKeyName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !creating) {
                  handleCreateAPIKey();
                }
              }}
              maxLength={50}
            />
            <p className="text-xs text-gray-500 mt-1">
              {newKeyName.length}/50 characters
            </p>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowCreateDialog(false);
                setNewKeyName('');
              }}
              disabled={creating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateAPIKey}
              disabled={creating || !newKeyName.trim()}
            >
              {creating ? 'Creating...' : 'Create API Key'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New API Key Display Dialog */}
      <Dialog open={showNewKeyDialog} onOpenChange={setShowNewKeyDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              API Key Created Successfully
            </DialogTitle>
            <DialogDescription>
              Copy your API key now. For security reasons, you won&apos;t be
              able to see it again.
            </DialogDescription>
          </DialogHeader>

          {newAPIKey && (
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">
                  Name
                </label>
                <Input className="mt-1" value={newAPIKey.name} readOnly />
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">
                  API Key
                </label>
                <div className="mt-1 flex">
                  <Input
                    className="flex-1 font-mono rounded-r-none -mr-px"
                    value={newAPIKey.key}
                    readOnly
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(newAPIKey.key)}
                    className="rounded-l-none h-9"
                  >
                    {copied ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                <div className="flex items-start">
                  <AlertTriangle className="w-4 h-4 text-amber-600 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-amber-700">
                    <strong>Important:</strong> Make sure to copy your API key
                    now. You won&apos;t be able to see it again after closing
                    this dialog.
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              onClick={() => {
                setShowNewKeyDialog(false);
                setNewAPIKey(null);
                setCopied(false);
              }}
            >
              I&apos;ve Copied My Key
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
