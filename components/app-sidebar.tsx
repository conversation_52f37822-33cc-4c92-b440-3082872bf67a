'use client';

import * as React from 'react';
import Link from 'next/link';
import {
  LayoutDashboard,
  Puzzle,
  Code2,
  PlusCircle,
  Sparkles,
  Webhook,
} from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { NavigationGroup, NavMain } from './nav-main';
import { NavCreditsUsage } from './nav-credits-usage';
import { NavUser } from './nav-user';
import { LogoWithTitle } from './logo-with-title';
import { JobCreateButton } from './jobs/create-button';
import { useRouter } from 'next/navigation';

const navGroups: NavigationGroup[] = [
  {
    items: [
      {
        title: 'Dashboard',
        url: '/dashboard',
        icon: LayoutDashboard,
      },
    ],
  },
  {
    title: 'Extraction',
    items: [
      {
        title: 'Templates',
        url: '/dashboard/templates',
        icon: Puzzle,
      },
      {
        title: 'Jobs',
        url: '/dashboard/jobs',
        icon: Sparkles,
      },
    ],
  },
  {
    title: 'Developer',
    items: [
      {
        title: 'API Keys',
        url: '/dashboard/api-keys',
        icon: Code2,
      },
      {
        title: 'Webhooks',
        url: '/dashboard/webhooks',
        icon: Webhook,
      },
    ],
  },
];

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const router = useRouter();

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/">
                <LogoWithTitle />
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              <SidebarMenuItem className="flex items-center gap-2">
                <JobCreateButton
                  onComplete={() => router.push('/dashboard/jobs')}
                >
                  <SidebarMenuButton className="bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground min-w-8 duration-200 ease-linear">
                    <PlusCircle />
                    <span>New Job</span>
                  </SidebarMenuButton>
                </JobCreateButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <NavMain groups={navGroups} />
      </SidebarContent>

      <SidebarFooter className="gap-4">
        <NavCreditsUsage />
        <SidebarSeparator className="mx-0" />
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
