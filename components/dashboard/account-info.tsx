'use client';

import { useUser } from '@/contexts/user-context';
import { Skeleton } from '@/components/ui/skeleton';
import { getPlanLabel } from '@/app/config';
import { SubscriptionPlan } from '@/app/types';
import { format } from 'date-fns';

export function AccountInfo() {
  const { user } = useUser();

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-8">
      <h2 className="text-xl font-semibold mb-4">Account Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <p className="text-sm text-gray-600">Email</p>
          {user ? (
            <p className="font-medium">{user.email}</p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Credits Available</p>
          {user ? (
            <p className="font-medium text-green-600">{user.credits}</p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Subscription Plan</p>
          {user ? (
            <p className="font-medium">
              {getPlanLabel(user.plan as SubscriptionPlan)}
            </p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Member Since</p>
          {user ? (
            <p className="font-medium">{format(user.createdAt, 'PPP')}</p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
      </div>
    </div>
  );
}
