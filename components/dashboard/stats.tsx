import {
  Card,
  CardContent,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { FileText, LayoutTemplate, Key, Share2 } from 'lucide-react';

interface StatsProps {
  jobs: number;
  templates: number;
  apiKeys: number;
  webhooks: number;
}

const statsConfig = [
  {
    label: 'Documents Processed',
    icon: FileText,
    color: 'bg-blue-100 text-blue-600',
    key: 'jobs',
  },
  {
    label: 'Templates',
    icon: LayoutTemplate,
    color: 'bg-green-100 text-green-600',
    key: 'templates',
  },
  {
    label: 'API Keys',
    icon: Key,
    color: 'bg-yellow-100 text-yellow-600',
    key: 'apiKeys',
  },
  {
    label: 'Webhooks',
    icon: Share2,
    color: 'bg-purple-100 text-purple-600',
    key: 'webhooks',
  },
];

export function Stats(props: StatsProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      {statsConfig.map(({ label, icon: Icon, color, key }) => (
        <Card
          key={key}
          className="transition-shadow hover:shadow-lg group cursor-pointer border-0 shadow-sm"
        >
          <CardContent className="flex flex-col items-center py-6">
            <div
              className={`rounded-full p-3 mb-3 ${color} group-hover:scale-105 transition-transform`}
              aria-hidden="true"
            >
              <Icon size={28} />
            </div>
            <CardTitle className="text-2xl font-bold mb-1">
              {props[key as keyof StatsProps]}
            </CardTitle>
            <CardDescription className="text-center text-base">
              {label}
            </CardDescription>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
