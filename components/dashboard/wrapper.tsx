import {
  B<PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { SidebarTrigger } from '@/components/ui/sidebar';

interface DashboardWrapperProps {
  children: React.ReactNode;
  breadcrumb?: {
    title: string;
    href: string;
  }[];
  className?: string;
}

export function DashboardWrapper({
  children,
  breadcrumb,
  className,
}: DashboardWrapperProps) {
  return (
    <>
      <header className="flex h-(--header-height) shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
        <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 !h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              {breadcrumb && breadcrumb.length > 0 && (
                <BreadcrumbSeparator className="hidden md:block" />
              )}
              {breadcrumb &&
                breadcrumb.map((item, index) => (
                  <div key={item.title} className="flex items-center gap-2">
                    <BreadcrumbItem>
                      {breadcrumb.length > 1 &&
                        breadcrumb.length - 1 !== index && (
                          <BreadcrumbLink href={item.href}>
                            {item.title}
                          </BreadcrumbLink>
                        )}
                      {breadcrumb.length - 1 === index && (
                        <BreadcrumbPage>{item.title}</BreadcrumbPage>
                      )}
                    </BreadcrumbItem>
                    {breadcrumb.length - 1 !== index && (
                      <BreadcrumbSeparator className="hidden md:block" />
                    )}
                  </div>
                ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>
      <div className="flex flex-1 flex-col">
        <div
          className={cn(
            '@container/main flex flex-1 flex-col gap-2',
            className,
          )}
        >
          {children}
        </div>
      </div>
    </>
  );
}
