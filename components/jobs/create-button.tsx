'use client';

import { ReactNode, useState } from 'react';
import { mutate } from 'swr';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DocumentUploadWithTemplate } from './document-upload-with-template';

interface JobCreateButtonProps {
  children: ReactNode;
  onComplete?: () => void;
}

export function JobCreateButton({
  children,
  onComplete,
}: JobCreateButtonProps) {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);

  const handleUploadComplete = () => {
    setUploadDialogOpen(false);
    mutate('/api/jobs');
    onComplete?.();
  };

  const handleUploadCancel = () => {
    setUploadDialogOpen(false);
  };

  return (
    <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Upload a Document</DialogTitle>
        </DialogHeader>
        <DocumentUploadWithTemplate
          onComplete={handleUploadComplete}
          onCancel={handleUploadCancel}
        />
      </DialogContent>
    </Dialog>
  );
}
