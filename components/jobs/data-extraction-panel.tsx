'use client';

import { useState, useEffect } from 'react';
import useS<PERSON> from 'swr';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertTriangle, Info } from 'lucide-react';
import { Job } from '@/lib/db';
import { Prisma } from '@prisma/client/runtime/library';

const fetcher = async (url: string): Promise<Job> => {
  const response = await fetch(url);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch job');
  }

  return result.data;
};

interface DataExtractionPanelProps {
  jobId?: string;
  onFieldFocus?: (field: string) => void;
  onFieldBlur?: () => void;
}

export function DataExtractionPanel({
  jobId,
  onFieldFocus,
  onFieldBlur,
}: DataExtractionPanelProps) {
  const [extractedData, setExtractedData] = useState<Record<
    string,
    Prisma.JsonValue
  > | null>(null);

  const { data: job, error } = useSWR(
    jobId ? `/api/jobs/${jobId}` : null,
    fetcher,
  );

  useEffect(() => {
    if (job?.extractedData) {
      setExtractedData(job.extractedData as Record<string, Prisma.JsonValue>);
    } else {
      setExtractedData(null);
    }
  }, [job]);

  const handleInputChange = (field: string, value: string) => {
    if (extractedData) {
      setExtractedData((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleApprove = () => {
    // In real app, this would save the data to the backend
    console.log('Approving extracted data:', extractedData);
  };

  if (!jobId) {
    return (
      <div className="w-full md:w-1/3 lg:w-1/4 xl:w-1/4 bg-white border-l border-gray-200 flex flex-col">
        <div className="p-4 border-b">
          <h2 className="text-lg font-semibold">Extracted Data</h2>
        </div>
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
              <svg
                className="w-8 h-8 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <p className="text-gray-500 text-sm">
              Select a document to view extracted data
            </p>
          </div>
        </div>
      </div>
    );
  }

  const renderContent = () => {
    if (error) {
      return (
        <div className="p-4 text-center text-red-500">
          <AlertTriangle className="mx-auto h-8 w-8" />
          <p className="mt-2 text-sm">Failed to load extracted data.</p>
        </div>
      );
    }

    if (!job) {
      return (
        <div className="p-4 space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i}>
              <Skeleton className="h-4 w-1/3 mb-2" />
              <Skeleton className="h-10 w-full" />
            </div>
          ))}
        </div>
      );
    }

    if (!extractedData || Object.keys(extractedData).length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          <Info className="mx-auto h-8 w-8" />
          <p className="mt-2 text-sm">No extraction data available.</p>
          <p className="text-xs">
            The document may still be processing, or no data was found.
          </p>
        </div>
      );
    }

    return (
      <>
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {Object.entries(extractedData).map(([field, value]) => (
            <div key={field} className="form-field">
              <label className="text-sm font-medium text-gray-600 flex items-center">
                {field}
              </label>
              <Input
                type={field.includes('date') ? 'date' : 'text'}
                value={value || ''}
                onChange={(e) => handleInputChange(field, e.target.value)}
                onFocus={() => onFieldFocus?.(field)}
                onBlur={() => onFieldBlur?.()}
                className="mt-1"
                data-field={field}
              />
            </div>
          ))}
        </div>
        <div className="p-4 border-t bg-white">
          <Button
            onClick={handleApprove}
            className="w-full bg-green-600 hover:bg-green-700"
          >
            Approve & Finalize
          </Button>
        </div>
      </>
    );
  };

  return (
    <div className="w-full md:w-1/3 lg:w-1/4 xl:w-1/4 bg-white border-l border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b flex justify-between items-center">
        <h2 className="text-lg font-semibold">Extracted Data</h2>
      </div>
      {renderContent()}
    </div>
  );
}
