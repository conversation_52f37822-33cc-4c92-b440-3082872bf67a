'use client';

import useS<PERSON> from 'swr';
import {
  Upload<PERSON>loud,
  FileText,
  Alert<PERSON>riangle,
  Loader2,
  Puzzle,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Job } from '@/lib/db';
import { JobCreateButton } from './create-button';

const fetcher = async (url: string): Promise<Job[]> => {
  const response = await fetch(url);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch jobs');
  }

  return result.data;
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800';
    case 'PROCESSING':
      return 'bg-blue-100 text-blue-800';
    case 'COMPLETED':
      return 'bg-green-100 text-green-800';
    case 'FAILED':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusText = (status: string) => {
  return status.charAt(0) + status.slice(1).toLowerCase();
};

interface DocumentListPanelProps {
  selectedJobId?: string;
  onJobSelect?: (jobId: string) => void;
}

export function DocumentListPanel({
  selectedJobId,
  onJobSelect,
}: DocumentListPanelProps) {
  const { data: jobs, error } = useSWR('/api/jobs', fetcher);

  return (
    <div className="w-full md:w-1/3 lg:w-1/4 xl:w-1/5 border-r border-gray-200 flex flex-col">
      {/* Upload Button */}
      <div className="p-4 border-b">
        <JobCreateButton>
          <Button className="w-full">
            <UploadCloud className="mr-2 h-4 w-4" />
            Upload Document
          </Button>
        </JobCreateButton>
      </div>

      {/* Document List */}
      <div className="flex-1 overflow-y-auto p-2 space-y-1">
        {error && (
          <div className="p-4 text-center text-red-500">
            <AlertTriangle className="mx-auto h-8 w-8" />
            <p className="mt-2 text-sm">Failed to load jobs.</p>
          </div>
        )}
        {!jobs && !error && (
          <div className="p-4 text-center text-muted-foreground">
            <Loader2 className="mx-auto h-8 w-8 animate-spin" />
            <p className="mt-2 text-sm">Loading jobs...</p>
          </div>
        )}
        {jobs && jobs.length === 0 && (
          <div className="p-4 text-center text-muted-foreground">
            <FileText className="mx-auto h-8 w-8" />
            <p className="mt-2 text-sm">No jobs found.</p>
            <p className="text-xs">
              Upload your first document to get started.
            </p>
          </div>
        )}
        {jobs &&
          jobs.map((job: Job) => (
            <div
              key={job.id}
              className={`
              block p-3 rounded-md cursor-pointer transition-colors
              ${
                selectedJobId === job.id
                  ? 'bg-muted text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                  : 'hover:bg-accent hover:text-accent-foreground'
              }
            `}
              onClick={() => onJobSelect?.(job.id)}
            >
              <p
                className="font-semibold text-sm truncate"
                title={job.fileName}
              >
                {job.fileName}
              </p>
              <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                <Puzzle className="w-3 h-3 flex-shrink-0" />
                <span>{job.template?.name || 'No template'}</span>
              </div>
              <div className="flex items-center gap-1 justify-between text-xs mt-2">
                <Badge variant="outline" className={getStatusColor(job.status)}>
                  {getStatusText(job.status)}
                </Badge>
                <span className="text-right text-muted-foreground">
                  {formatDistanceToNow(new Date(job.createdAt))}
                </span>
              </div>
            </div>
          ))}
      </div>
    </div>
  );
}
