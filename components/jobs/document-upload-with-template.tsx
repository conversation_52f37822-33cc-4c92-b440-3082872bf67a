'use client';

import { useState } from 'react';
import { TemplateSelection } from './template-selection';
import { FileUploader } from './file-uploader';
import { Button } from '@/components/ui/button';
import { ArrowLeft, CheckCircle } from 'lucide-react';
import { TemplateField } from '@/components/templates/shared';

interface Template {
  id: string;
  name: string;
  description?: string;
  definition: Record<string, TemplateField>;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

type Step = 'template-selection' | 'file-upload';

interface DocumentUploadWithTemplateProps {
  onComplete?: () => void;
  onCancel?: () => void;
  template?: Template | null;
}

export function DocumentUploadWithTemplate({
  onComplete,
  onCancel,
  template,
}: DocumentUploadWithTemplateProps) {
  const [currentStep, setCurrentStep] = useState<Step>('template-selection');

  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    template || null,
  );

  const handleTemplateSelect = (template: Template) => {
    setSelectedTemplate(template);
    setCurrentStep('file-upload');
  };

  const handleBackToTemplateSelection = () => {
    setCurrentStep('template-selection');
    setSelectedTemplate(null);
  };

  const handleUploadComplete = () => {
    onComplete?.();
  };

  return (
    <div className="space-y-6">
      {/* Step Indicator */}
      <div className="flex items-center justify-center space-x-4">
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-primary text-primary-foreground">
            {currentStep === 'file-upload' ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              '1'
            )}
          </div>
          <span className="ml-2 text-sm font-medium text-gray-700">
            Select Template
          </span>
        </div>

        <div className="flex-1 h-0.5 bg-gray-200">
          <div
            className={`
              h-full transition-all duration-300
              ${
                currentStep === 'file-upload'
                  ? 'bg-primary w-full'
                  : 'bg-muted w-0'
              }
            `}
          />
        </div>

        <div className="flex items-center">
          <div
            className={`
              w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
              ${
                currentStep === 'file-upload'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted text-muted-foreground'
              }
            `}
          >
            2
          </div>
          <span
            className={`
              ml-2 text-sm font-medium
              ${
                currentStep === 'file-upload'
                  ? 'text-gray-700'
                  : 'text-gray-500'
              }
            `}
          >
            Upload Document
          </span>
        </div>
      </div>

      {/* Step Content */}
      <div className="min-h-[300px]">
        <div
          className={currentStep === 'template-selection' ? 'block' : 'hidden'}
        >
          <TemplateSelection
            onTemplateSelect={handleTemplateSelect}
            onCancel={onCancel}
          />
        </div>

        {selectedTemplate && (
          <div
            className={
              currentStep === 'file-upload' && selectedTemplate
                ? 'block'
                : 'hidden'
            }
          >
            <div className="space-y-4">
              {/* Selected Template Summary */}
              <div className="bg-primary/10 border border-primary/40 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-primary">
                      Selected Template: {selectedTemplate.name}
                    </h4>
                    {selectedTemplate.description && (
                      <p className="text-sm text-primary/80 mt-1">
                        {selectedTemplate.description}
                      </p>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleBackToTemplateSelection}
                  >
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    Change
                  </Button>
                </div>
              </div>

              {/* File Uploader */}
              <FileUploader
                selectedTemplate={selectedTemplate}
                onComplete={handleUploadComplete}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
