'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Upload, X, FileText, Image } from 'lucide-react';
import { useUpload } from '@/hooks/use-upload';

interface TemplateField {
  type: string;
  required: boolean;
  description?: string;
  properties?: Record<string, TemplateField>;
  items?: TemplateField;
}

interface Template {
  id: string;
  name: string;
  description?: string;
  definition: Record<string, TemplateField>;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

interface FileUploadState {
  file: File | null;
  uploading: boolean;
  progress: number;
  error: string | null;
  success: boolean;
}

interface FileUploaderProps {
  selectedTemplate?: Template;
  onComplete?: () => void;
}

export function FileUploader({
  selectedTemplate,
  onComplete,
}: FileUploaderProps) {
  const { uploadFile, error } = useUpload();
  const [state, setState] = useState<FileUploadState>({
    file: null,
    uploading: false,
    progress: 0,
    error: null,
    success: false,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const selectedFile = event.target.files[0];
      setState((prev) => ({
        ...prev,
        file: selectedFile,
        error: null,
        success: false,
      }));
    }
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    if (files && files[0]) {
      setState((prev) => ({
        ...prev,
        file: files[0],
        error: null,
        success: false,
      }));
    }

    // Remove drag styling
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('border-indigo-500', 'bg-indigo-50');
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    // Add drag styling
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.add('border-indigo-500', 'bg-indigo-50');
    }
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();

    // Remove drag styling
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('border-indigo-500', 'bg-indigo-50');
    }
  };

  const clearFile = () => {
    setState((prev) => ({
      ...prev,
      file: null,
      error: null,
      success: false,
      progress: 0,
    }));

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUpload = async () => {
    if (!state.file) return;
    if (!selectedTemplate) return;

    setState((prev) => ({
      ...prev,
      uploading: true,
      progress: 0,
      error: null,
    }));

    try {
      // Step 1: Get presigned URL
      setState((prev) => ({ ...prev, progress: 10 }));

      const uploadResult = await uploadFile(state.file, selectedTemplate!.id);

      if (!uploadResult.success) {
        throw new Error(error || 'Failed to upload file');
      }

      setState((prev) => ({ ...prev, progress: 80 }));

      setState((prev) => ({
        ...prev,
        progress: 100,
        success: true,
        uploading: false,
      }));

      // Clear file after successful upload
      setTimeout(() => {
        clearFile();
        onComplete?.();
      }, 2000);
    } catch (error) {
      console.error('Upload error:', error);
      setState((prev) => ({
        ...prev,
        uploading: false,
        error: error instanceof Error ? error.message : 'Upload failed',
        progress: 0,
      }));
    }
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return (
        <Image className="h-8 w-8 text-blue-500" aria-label="Image file" />
      );
    }
    return (
      <FileText className="h-8 w-8 text-red-500" aria-label="Document file" />
    );
  };

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        ref={dropZoneRef}
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-indigo-500 hover:bg-indigo-50 transition-colors"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-lg font-semibold text-gray-700">
          Drag & drop files here
        </p>
        <p className="text-gray-500">or click to browse</p>
        <p className="text-sm text-gray-400 mt-1">
          PDF, JPEG, PNG, GIF, WebP (max 10MB)
        </p>
      </div>

      {/* Hidden file input */}
      <Input
        ref={fileInputRef}
        type="file"
        onChange={handleFileChange}
        accept="application/pdf,image/*"
        className="hidden"
      />

      {/* Selected file display */}
      {state.file && (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-3">
            {getFileIcon(state.file)}
            <div>
              <p className="font-medium text-sm">{state.file.name}</p>
              <p className="text-xs text-gray-500">
                {(state.file.size / 1024 / 1024).toFixed(2)} MB
              </p>
            </div>
          </div>
          {!state.uploading && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFile}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      )}

      {/* Progress bar */}
      {state.uploading && (
        <div className="space-y-2">
          <Progress value={state.progress} className="w-full" />
          <p className="text-sm text-gray-600 text-center">
            Uploading... {state.progress}%
          </p>
        </div>
      )}

      {/* Error message */}
      {state.error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{state.error}</p>
        </div>
      )}

      {/* Success message */}
      {state.success && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-sm text-green-600">
            File uploaded successfully! Processing will begin shortly.
          </p>
        </div>
      )}

      {/* Upload button */}
      <Button
        onClick={handleUpload}
        disabled={!state.file || state.uploading}
        className="w-full"
      >
        {state.uploading ? 'Uploading...' : 'Upload Document'}
      </Button>
    </div>
  );
}
