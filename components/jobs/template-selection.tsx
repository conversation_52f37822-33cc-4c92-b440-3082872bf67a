import { useState } from 'react';
import useSWR from 'swr';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Globe, User, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Template, TemplateField } from '@/components/templates/shared';

export interface TemplatesData {
  public: Template[];
  user: Template[];
}

interface TemplateSelectionProps {
  onTemplateSelect: (template: Template) => void;
  onCancel?: () => void;
}

const fetcher = async (url: string): Promise<TemplatesData> => {
  const response = await fetch(url);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch templates');
  }

  return result.data;
};

export function TemplateSelection({
  onTemplateSelect,
  onCancel,
}: TemplateSelectionProps) {
  const {
    data: templates,
    error,
    isLoading,
    mutate,
  } = useSWR<TemplatesData>('/api/templates', fetcher);

  const [searchQuery, setSearchQuery] = useState('');

  const countFields = (
    fields: Record<string, TemplateField>,
  ): { total: number; required: number } => {
    let total = 0;
    let required = 0;

    Object.values(fields).forEach((field) => {
      total++;
      if (field.required) required++;

      if (field.type === 'object' && field.properties) {
        const nested = countFields(field.properties);
        total += nested.total;
        required += nested.required;
      }

      if (
        field.type === 'array' &&
        field.items?.type === 'object' &&
        field.items.properties
      ) {
        const nested = countFields(field.items.properties);
        total += nested.total;
        required += nested.required;
      }
    });

    return { total, required };
  };

  const allTemplates = templates
    ? [...templates.public, ...templates.user]
    : [];
  const filteredTemplates = allTemplates.filter(
    (template) =>
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (template.description &&
        template.description.toLowerCase().includes(searchQuery.toLowerCase())),
  );

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Select a Template</h3>
          <p className="text-sm text-gray-500 mt-1">
            Choose a template to guide the data extraction process for your
            document.
          </p>
        </div>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center space-y-4">
        <h3 className="text-lg font-semibold">Error Loading Templates</h3>
        <p className="text-sm text-red-600">{error}</p>
        <div className="flex gap-2 justify-center">
          <Button onClick={() => mutate()} variant="outline">
            Retry
          </Button>
          {onCancel && (
            <Button onClick={onCancel} variant="ghost">
              Cancel
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold">Select a Template</h3>
        <p className="text-sm text-gray-500 mt-1">
          Choose a template to guide the data extraction process for your
          document.
        </p>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          placeholder="Search templates..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 max-h-96 overflow-y-auto">
        {filteredTemplates.map((template) => {
          const { total: fieldCount, required: requiredFieldCount } =
            countFields(template.definition);

          return (
            <Card
              key={template.id}
              className="cursor-pointer hover:shadow-md hover:border-primary transition-all"
              onClick={() => onTemplateSelect(template)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-base font-semibold">
                      {template.name}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {template.description || 'No description provided'}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-1">
                    {template.userId ? (
                      <div title="Custom template">
                        <User className="h-4 w-4 text-gray-600" />
                      </div>
                    ) : (
                      <div title="Public template">
                        <Globe className="h-4 w-4 text-blue-600" />
                      </div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>{fieldCount} fields</span>
                  <span>{requiredFieldCount} required</span>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">
            No templates found matching your search.
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2">
        {onCancel && (
          <Button className="flex-1" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
      </div>
    </div>
  );
}
