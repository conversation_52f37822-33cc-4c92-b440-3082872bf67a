import { cn } from '@/lib/utils';

export function Logo({ className, ...props }: React.ComponentProps<'svg'>) {
  return (
    <svg
      width="24"
      height="24"
      fill="none"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="2"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('text-primary', className)}
      {...props}
    >
      <path d="m4 2s5.2029-0.045831 11 0c5.7971 0.045831 6.2757 7.1507 4 11-2.2757 3.8493-2.9336 8.3314 1.5 9h-14.5c-5.0533-1.2977-4.03-7.3639-2.1966-9.9563 1.8334-2.5924 4.2963-6.1967 0.19656-10.044z" />
      <path d="m9.6975 7.7765 7.3287 4.723e-4" />
      <path d="m8.0588 11.229h8.1773" />
      <path d="m5.8466 14.681h8.7304" />
      <path d="m5.4515 18.132h8.8094" />
    </svg>
  );
}
