'use client';

import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { useUser } from '@/contexts/user-context';
import { Skeleton } from './ui/skeleton';
import { getCreditsForPlan } from '@/app/config';
import { SubscriptionPlan } from '@/app/types';
import Link from 'next/link';

export function NavCreditsUsage() {
  const { user, loading, error } = useUser();

  if (loading && !user) {
    return (
      <div>
        <Skeleton className="h-4 bg-gray-200 mb-2 w-2/3" />
        <Skeleton className="h-2 bg-gray-200 mb-3" />
        <Skeleton className="h-8 bg-gray-200" />
      </div>
    );
  }

  if (error || !user) {
    return (
      <div>
        <p className="text-sm text-gray-500">Credits: Unavailable</p>
        <Button variant="secondary" size="sm" className="w-full mt-3" disabled>
          Upgrade Plan
        </Button>
      </div>
    );
  }

  const creditsTotal = getCreditsForPlan(user.plan as SubscriptionPlan);
  const creditsUsed = creditsTotal - user.credits;
  const creditsPercentage =
    creditsTotal > 0 ? (creditsUsed / creditsTotal) * 100 : 0;

  return (
    <div>
      <p className="text-sm text-gray-600">
        Credits Used: {creditsUsed} / {creditsTotal}
      </p>
      <Progress value={creditsPercentage} className="mt-2" />
      <Button asChild variant="secondary" size="sm" className="w-full mt-3">
        <Link href="/dashboard/billing">Upgrade Plan</Link>
      </Button>
    </div>
  );
}
