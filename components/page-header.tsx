import { ReactNode } from 'react';

export function PageHeader({
  children,
  actions,
}: {
  children: ReactNode;
  actions?: ReactNode;
}) {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
      <div className="space-y-1">{children}</div>
      {actions}
    </div>
  );
}

export function PageHeaderHeading({ children }: { children: ReactNode }) {
  return <h1 className="text-2xl font-bold text-gray-900">{children}</h1>;
}

export function PageHeaderDescription({ children }: { children: ReactNode }) {
  return <p className="text-gray-600">{children}</p>;
}
