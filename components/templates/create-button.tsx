'use client';

import { useState } from 'react';
import { mutate } from 'swr';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { TemplateCreateDialog } from './create-dialog';
import { TemplateField } from './shared';

export function TemplateCreateButton() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);

  const handleCreateTemplate = async (templateData: {
    name: string;
    description?: string;
    definition: Record<string, TemplateField>;
  }) => {
    try {
      setCreateLoading(true);
      const response = await fetch('/api/templates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(templateData),
      });

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to create template');
      }

      // Refresh templates list
      mutate('/api/templates');
      setCreateDialogOpen(false);
    } catch (err) {
      console.error('Error creating template:', err);
      // You might want to show a toast notification here
    } finally {
      setCreateLoading(false);
    }
  };

  return (
    <>
      <Button onClick={() => setCreateDialogOpen(true)}>
        <Plus className="h-4 w-4 mr-2" />
        Create Template
      </Button>

      <TemplateCreateDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateTemplate}
        loading={createLoading}
      />
    </>
  );
}
