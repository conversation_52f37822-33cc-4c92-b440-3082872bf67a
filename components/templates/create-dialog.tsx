import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alog<PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useTemplateDialog } from './use-template-dialog';
import { TemplateFieldEditor } from './field-editor';
import { TemplateField } from './shared';

interface TemplateCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (template: {
    name: string;
    description?: string;
    definition: Record<string, TemplateField>;
  }) => Promise<void>;
  loading?: boolean;
}

export function TemplateCreateDialog({
  open,
  onOpenChange,
  onSubmit,
  loading = false,
}: TemplateCreateDialogProps) {
  const {
    name,
    setName,
    description,
    setDescription,
    fields,
    expandedFields,
    newField,
    setNewField,
    fieldRows,
    toggleExpanded,
    deleteField,
    addField,
    onFieldChange,
    onFieldNameChange,
  } = useTemplateDialog({ open });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || Object.keys(fields).length === 0) return;

    try {
      await onSubmit({
        name: name.trim(),
        description: description.trim(),
        definition: fields,
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save template:', error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Template</DialogTitle>
            <DialogDescription>
              Create a custom template for extracting data from your documents.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-6">
            <div>
              <label className="text-sm font-medium">Template Name</label>
              <Input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Custom Invoice Template"
              />
            </div>

            <div>
              <label className="text-sm font-medium">
                Description (Optional)
              </label>
              <Input
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what this template is for..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">Fields</label>

              <TemplateFieldEditor
                fieldRows={fieldRows}
                newField={newField}
                expandedFields={expandedFields}
                onToggleExpanded={toggleExpanded}
                onDeleteField={deleteField}
                onAddField={addField}
                onNewFieldChange={(changes) =>
                  setNewField((prev) => ({ ...prev, ...changes }))
                }
                onFieldChange={onFieldChange}
                onFieldNameChange={onFieldNameChange}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                loading || !name.trim() || Object.keys(fields).length === 0
              }
            >
              {loading ? 'Creating...' : 'Create Template'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
