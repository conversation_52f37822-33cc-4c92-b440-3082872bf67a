import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { TemplateFieldEditor } from './field-editor';
import { useTemplateDialog } from './use-template-dialog';
import { Template, TemplateField } from './shared';

interface TemplateEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  template: Template | null;
  onSubmit: (templateData: {
    name: string;
    description?: string;
    definition: Record<string, TemplateField>;
  }) => Promise<void>;
  loading?: boolean;
}

export function TemplateEditDialog({
  open,
  onOpenChange,
  template,
  onSubmit,
  loading = false,
}: TemplateEditDialogProps) {
  const {
    name,
    setName,
    description,
    setDescription,
    fields,
    expandedFields,
    newField,
    setNewField,
    fieldRows,
    toggleExpanded,
    deleteField,
    addField,
    onFieldChange,
    onFieldNameChange,
  } = useTemplateDialog({ template, open });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || Object.keys(fields).length === 0) return;

    try {
      await onSubmit({
        name: name.trim(),
        description: description.trim(),
        definition: fields,
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to update template:', error);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[80vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Edit Template</DialogTitle>
            <DialogDescription>
              Modify your custom template for extracting data from documents.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-6">
            <div>
              <label className="text-sm font-medium">Template Name</label>
              <Input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="e.g., Custom Invoice Template"
                required
              />
            </div>

            <div>
              <label className="text-sm font-medium">
                Description (Optional)
              </label>
              <Input
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe what this template is for..."
              />
            </div>

            <div>
              <label className="text-sm font-medium">Fields</label>

              <TemplateFieldEditor
                fieldRows={fieldRows}
                newField={newField}
                expandedFields={expandedFields}
                onToggleExpanded={toggleExpanded}
                onDeleteField={deleteField}
                onAddField={addField}
                onNewFieldChange={(changes) =>
                  setNewField((prev) => ({ ...prev, ...changes }))
                }
                onFieldChange={onFieldChange}
                onFieldNameChange={onFieldNameChange}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => handleOpenChange(false)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={
                loading || !name.trim() || Object.keys(fields).length === 0
              }
            >
              {loading ? 'Updating...' : 'Update Template'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
