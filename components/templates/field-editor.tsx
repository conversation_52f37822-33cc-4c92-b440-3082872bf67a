import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, X, ChevronRight, ChevronDown } from 'lucide-react';
import { FIELD_TYPES, FieldRow, NewFieldState, TemplateField } from './shared';

interface TemplateFieldEditorProps {
  fieldRows: FieldRow[];
  newField: NewFieldState;
  expandedFields: Set<string>;
  onToggleExpanded: (path: string) => void;
  onDeleteField: (row: FieldRow) => void;
  onAddField: () => void;
  onNewFieldChange: (field: Partial<NewFieldState>) => void;
  onFieldChange: (path: string, field: Partial<TemplateField>) => void;
  onFieldNameChange: (oldPath: string, newName: string) => void;
}

export function TemplateFieldEditor({
  fieldRows,
  newField,
  expandedFields,
  onToggleExpanded,
  onDeleteField,
  onAddField,
  onNewFieldChange,
  onFieldChange,
  onFieldNameChange,
}: TemplateFieldEditorProps) {
  const canExpand = (field: TemplateField) => {
    return (
      field.type === 'object' ||
      (field.type === 'array' && field.items?.type === 'object')
    );
  };

  const renderFieldRow = (row: FieldRow) => {
    const isExpanded = expandedFields.has(row.path);
    const canExpandField = canExpand(row.field);

    return (
      <tr key={row.id} className="border-t hover:bg-gray-50">
        <td className="px-4 py-2">
          <div
            className="flex items-center"
            style={{ paddingLeft: `${row.level * 20}px` }}
          >
            {canExpandField && (
              <button
                type="button"
                onClick={() => onToggleExpanded(row.path)}
                className="mr-2 p-1 hover:bg-gray-200 rounded"
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </button>
            )}

            <Input
              value={row.name}
              onChange={(e) => onFieldNameChange(row.path, e.target.value)}
              className="h-8 w-32 font-medium"
            />
          </div>
        </td>

        <td className="px-4 py-2">
          <div className="space-y-2">
            <select
              value={row.field.type}
              onChange={(e) => {
                const newField = { ...row.field, type: e.target.value };
                if (e.target.value === 'array' && !newField.items) {
                  newField.items = { type: 'string', required: false };
                }
                onFieldChange(row.path, newField);
              }}
              className="h-8 px-2 py-1 text-sm border border-input rounded-md bg-white w-full"
            >
              {FIELD_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {row.field.type === 'array' && (
              <select
                value={row.field.items?.type || 'string'}
                onChange={(e) =>
                  onFieldChange(row.path, {
                    ...row.field,
                    items: { type: e.target.value, required: false },
                  })
                }
                className="h-8 px-2 py-1 text-sm border border-input rounded-md bg-white w-full"
              >
                {FIELD_TYPES.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            )}
          </div>
        </td>

        <td className="px-4 py-2">
          <input
            type="checkbox"
            checked={row.field.required || false}
            onChange={(e) =>
              onFieldChange(row.path, {
                ...row.field,
                required: e.target.checked,
              })
            }
            className="rounded"
          />
        </td>

        <td className="px-4 py-2">
          <Input
            value={row.field.description || ''}
            onChange={(e) =>
              onFieldChange(row.path, {
                ...row.field,
                description: e.target.value,
              })
            }
            placeholder="Description..."
            className="h-8"
          />
        </td>

        <td className="px-4 py-2">
          <div className="flex items-center gap-1">
            {(row.field.type === 'object' ||
              (row.field.type === 'array' &&
                row.field.items?.type === 'object')) && (
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={() => onNewFieldChange({ parentPath: row.path })}
                className="h-8 w-8 p-0 text-blue-500 hover:text-blue-700"
                title="Add nested field"
              >
                <Plus className="h-3 w-3" />
              </Button>
            )}
            <Button
              type="button"
              size="sm"
              variant="ghost"
              onClick={() => onDeleteField(row)}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className="space-y-4">
      {fieldRows.length > 0 ? (
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Field Name
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Type
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Required
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Description
                </th>
                <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>{fieldRows.map(renderFieldRow)}</tbody>
          </table>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg">
          No fields added yet. Add your first field below.
        </div>
      )}

      {/* Add new field form */}
      <div className="p-4 border rounded-lg bg-gray-50">
        <h4 className="text-sm font-medium mb-3">
          {newField.parentPath
            ? `Add nested field to: ${newField.parentPath}`
            : 'Add New Field'}
        </h4>
        {newField.parentPath && (
          <Button
            type="button"
            size="sm"
            variant="outline"
            onClick={() => onNewFieldChange({ parentPath: '' })}
            className="mb-3"
          >
            <X className="h-3 w-3 mr-1" />
            Cancel nested field
          </Button>
        )}
        <div className="grid grid-cols-2 gap-3 mb-3">
          <div>
            <label className="text-xs font-medium text-gray-700">
              Field Name
            </label>
            <Input
              value={newField.name}
              onChange={(e) => onNewFieldChange({ name: e.target.value })}
              placeholder="Field name"
              className="h-9"
            />
          </div>
          <div>
            <label className="text-xs font-medium text-gray-700">Type</label>
            <select
              value={newField.type}
              onChange={(e) => onNewFieldChange({ type: e.target.value })}
              className="h-9 px-3 py-1 text-sm border border-input rounded-md bg-white w-full"
            >
              {FIELD_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mb-3">
          <label className="text-xs font-medium text-gray-700">
            Description
          </label>
          <Input
            value={newField.description || ''}
            onChange={(e) => onNewFieldChange({ description: e.target.value })}
            placeholder="Field description"
            className="h-9"
          />
        </div>

        {newField.type === 'array' && (
          <div className="mb-3">
            <label className="text-xs font-medium text-gray-700">
              Array Item Type
            </label>
            <select
              value={newField.arrayItemType}
              onChange={(e) =>
                onNewFieldChange({ arrayItemType: e.target.value })
              }
              className="h-9 px-3 py-1 text-sm border border-input rounded-md bg-white w-full"
            >
              {FIELD_TYPES.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>
        )}

        <div className="flex items-center justify-between">
          <label className="flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={newField.required}
              onChange={(e) => onNewFieldChange({ required: e.target.checked })}
              className="rounded"
            />
            Required
          </label>
          <Button
            type="button"
            size="sm"
            onClick={onAddField}
            disabled={!newField.name.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Field
          </Button>
        </div>
      </div>
    </div>
  );
}
