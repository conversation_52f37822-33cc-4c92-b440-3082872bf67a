export const FIELD_TYPES = [
  { value: 'string', label: 'Text' },
  { value: 'number', label: 'Number' },
  { value: 'date', label: 'Date' },
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'array', label: 'List' },
  { value: 'object', label: 'Object' },
];

export interface TemplateField {
  type: string;
  required: boolean;
  description?: string;
  properties?: Record<string, TemplateField>; // For object types
  items?: TemplateField; // For array types
}

export interface Template {
  id: string;
  name: string;
  description?: string;
  definition: Record<string, TemplateField>;
  userId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FieldRow {
  id: string;
  path: string;
  name: string;
  field: TemplateField;
  level: number;
  parentPath?: string;
  isArrayItem?: boolean;
}

export interface NewFieldState {
  name: string;
  type: string;
  required: boolean;
  arrayItemType: string;
  parentPath: string;
  description?: string;
}

export const DEFAULT_NEW_FIELD: NewFieldState = {
  name: '',
  type: 'string',
  required: false,
  arrayItemType: 'string',
  parentPath: '',
  description: '',
};
