'use client';

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, User, Globe, Copy, Edit, Trash2 } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Template, TemplateField } from './shared';

interface TemplateCardProps {
  template: Template;
  onUse?: (template: Template) => void;
  onEdit?: (template: Template) => void;
  onDelete?: (template: Template) => void;
  onCopy?: (template: Template) => void;
}

export function TemplateCard({
  template,
  onUse,
  onEdit,
  onDelete,
  onCopy,
}: TemplateCardProps) {
  const countFields = (
    fields: Record<string, TemplateField>,
  ): { total: number; required: number } => {
    let total = 0;
    let required = 0;

    Object.values(fields).forEach((field) => {
      total++;
      if (field.required) required++;

      // Count nested fields in objects
      if (field.type === 'object' && field.properties) {
        const nested = countFields(field.properties);
        total += nested.total;
        required += nested.required;
      }

      // Count nested fields in array items (if object type)
      if (
        field.type === 'array' &&
        field.items?.type === 'object' &&
        field.items.properties
      ) {
        const nested = countFields(field.items.properties);
        total += nested.total;
        required += nested.required;
      }
    });

    return { total, required };
  };

  const { total: fieldCount, required: requiredFieldCount } = countFields(
    template.definition,
  );

  return (
    <Card className="h-full hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold truncate">
              {template.name}
            </CardTitle>
            <CardDescription
              className="mt-1 text-sm text-gray-600 overflow-hidden"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
              }}
            >
              {template.description || 'No description provided'}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2 ml-2">
            {template.userId ? (
              <div title="Custom template">
                <User className="h-4 w-4 text-gray-600" />
              </div>
            ) : (
              <div title="Public template">
                <Globe className="h-4 w-4 text-blue-600" />
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Fields:</span>
            <span className="font-medium">{fieldCount} total</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Required:</span>
            <span className="font-medium text-red-600">
              {requiredFieldCount}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-1 text-xs text-gray-500">
          <Calendar className="h-3 w-3" />
          <span>
            {template.userId
              ? `Created ${new Date(template.createdAt).toLocaleDateString()}`
              : 'Built-in template'}
          </span>
        </div>

        <div className="flex gap-2 pt-2">
          {onUse && (
            <Button
              size="sm"
              className="flex-1"
              onClick={() => onUse(template)}
            >
              Use Template
            </Button>
          )}
          {onCopy && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onCopy(template)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Copy template</TooltipContent>
            </Tooltip>
          )}
          {template.userId && onEdit && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onEdit(template)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Edit template</TooltipContent>
            </Tooltip>
          )}
          {template.userId && onDelete && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onDelete(template)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Delete template</TooltipContent>
            </Tooltip>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
