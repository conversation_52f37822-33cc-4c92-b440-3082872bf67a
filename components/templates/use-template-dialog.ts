'use client';

import { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  FieldRow,
  NewFieldState,
  DEFAULT_NEW_FIELD,
  Template,
  TemplateField,
} from './shared';

interface UseTemplateDialogProps {
  template?: Template | null;
  open: boolean;
}

export function useTemplateDialog({ template, open }: UseTemplateDialogProps) {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [fields, setFields] = useState<Record<string, TemplateField>>({});
  const [fieldIds, setFieldIds] = useState<Map<string, string>>(new Map()); // Map path to stable UUID
  const [expandedFields, setExpandedFields] = useState<Set<string>>(new Set());
  const [newField, setNewField] = useState<NewFieldState>(DEFAULT_NEW_FIELD);

  // Initialize form with template data when template changes
  useEffect(() => {
    if (template) {
      setName(template.name);
      setDescription(template.description || '');
      setFields(template.definition);
      setFieldIds(new Map());
      setExpandedFields(new Set());
      setNewField(DEFAULT_NEW_FIELD);
    } else if (open) {
      // Reset form when opening for create (no template)
      setName('');
      setDescription('');
      setFields({});
      setFieldIds(new Map());
      setExpandedFields(new Set());
      setNewField(DEFAULT_NEW_FIELD);
    }
  }, [template, open]);

  // Helper function to get or create a stable ID for a field path
  const getStableId = (path: string): string => {
    if (!fieldIds.has(path)) {
      setFieldIds((prev) => new Map(prev).set(path, uuidv4()));
      return uuidv4(); // Return a temporary ID until the state updates
    }
    return fieldIds.get(path)!;
  };

  const flattenFields = (
    fieldsObj: Record<string, TemplateField>,
    parentPath = '',
    level = 0,
  ): FieldRow[] => {
    const rows: FieldRow[] = [];

    Object.entries(fieldsObj).forEach(([fieldName, field]) => {
      const currentPath = parentPath ? `${parentPath}.${fieldName}` : fieldName;

      rows.push({
        id: getStableId(currentPath),
        path: currentPath,
        name: fieldName,
        field,
        level,
        parentPath: parentPath || undefined,
      });

      // Add object properties if expanded
      if (
        field.type === 'object' &&
        field.properties &&
        expandedFields.has(currentPath)
      ) {
        rows.push(...flattenFields(field.properties, currentPath, level + 1));
      }

      // Add array item properties if it's an object array and expanded
      if (
        field.type === 'array' &&
        field.items?.type === 'object' &&
        field.items.properties &&
        expandedFields.has(currentPath)
      ) {
        const arrayItemRows = flattenFields(
          field.items.properties,
          `${currentPath}[]`,
          level + 1,
        );
        arrayItemRows.forEach((row) => {
          row.isArrayItem = true;
        });
        rows.push(...arrayItemRows);
      }
    });

    return rows;
  };

  const toggleExpanded = (path: string) => {
    setExpandedFields((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const deleteField = (row: FieldRow) => {
    const pathParts = row.path.split('.');
    const fieldName = pathParts[pathParts.length - 1];
    const parentPath = pathParts.slice(0, -1).join('.');

    setFields((prev) => {
      const newFields = { ...prev };

      if (parentPath) {
        // Deleting nested field
        const deleteNestedField = (
          obj: Record<string, TemplateField>,
          path: string[],
          fieldToDelete: string,
        ) => {
          if (path.length === 0) {
            delete obj[fieldToDelete];
          } else {
            const [first, ...rest] = path;
            if (first.endsWith('[]')) {
              const arrayField = first.slice(0, -2);
              if (obj[arrayField]?.items?.properties) {
                deleteNestedField(
                  obj[arrayField].items.properties!,
                  rest,
                  fieldToDelete,
                );
              }
            } else {
              if (obj[first]?.properties) {
                deleteNestedField(obj[first].properties!, rest, fieldToDelete);
              }
            }
          }
        };

        deleteNestedField(newFields, parentPath.split('.'), fieldName);
      } else {
        // Deleting top-level field
        delete newFields[fieldName];
      }

      return newFields;
    });

    // Remove from expanded fields if it was expanded
    setExpandedFields((prev) => {
      const newSet = new Set(prev);
      newSet.delete(row.path);
      return newSet;
    });

    // Remove the stable ID mapping for this path and any child paths
    setFieldIds((prev) => {
      const newMap = new Map(prev);
      // Remove current path
      newMap.delete(row.path);
      // Remove any child paths that start with this path
      for (const [path] of newMap) {
        if (
          path.startsWith(row.path + '.') ||
          path.startsWith(row.path + '[]')
        ) {
          newMap.delete(path);
        }
      }
      return newMap;
    });
  };

  const addField = () => {
    if (!newField.name.trim()) return;

    const fieldObj: TemplateField = {
      type: newField.type,
      required: newField.required,
      description: newField.description,
    };

    if (newField.type === 'array') {
      if (newField.arrayItemType === 'object') {
        fieldObj.items = {
          type: 'object',
          required: false,
          properties: {},
        };
      } else {
        fieldObj.items = {
          type: newField.arrayItemType,
          required: false,
        };
      }
    } else if (newField.type === 'object') {
      fieldObj.properties = {};
    }

    setFields((prev) => {
      const newFields = { ...prev };

      if (newField.parentPath) {
        // Adding nested field
        const addNestedField = (
          obj: Record<string, TemplateField>,
          path: string[],
          fieldName: string,
          value: TemplateField,
        ) => {
          if (path.length === 1) {
            if (path[0].endsWith('[]')) {
              const arrayField = path[0].slice(0, -2);
              if (!obj[arrayField]?.items?.properties) {
                obj[arrayField] = {
                  ...obj[arrayField],
                  items: { type: 'object', required: false, properties: {} },
                };
              }
              obj[arrayField]!.items!.properties![fieldName] = value;
            } else {
              if (!obj[path[0]]?.properties) {
                obj[path[0]] = {
                  ...obj[path[0]],
                  type: 'object',
                  required: false,
                  properties: {},
                };
              }
              obj[path[0]].properties![fieldName] = value;
            }
          } else {
            const [first, ...rest] = path;
            if (first.endsWith('[]')) {
              const arrayField = first.slice(0, -2);
              if (!obj[arrayField]?.items?.properties) {
                obj[arrayField] = {
                  ...obj[arrayField],
                  items: { type: 'object', required: false, properties: {} },
                };
              }
              addNestedField(
                obj[arrayField]!.items!.properties!,
                rest,
                fieldName,
                value,
              );
            } else {
              if (!obj[first]?.properties) {
                obj[first] = { ...obj[first], required: false, properties: {} };
              }
              addNestedField(obj[first].properties!, rest, fieldName, value);
            }
          }
        };

        addNestedField(
          newFields,
          newField.parentPath.split('.'),
          newField.name,
          fieldObj,
        );
      } else {
        // Adding top-level field
        newFields[newField.name] = fieldObj;
      }

      return newFields;
    });

    setNewField(DEFAULT_NEW_FIELD);
  };

  const onFieldChange = (path: string, changes: Partial<TemplateField>) => {
    const pathParts = path.split('.');
    const fieldName = pathParts[pathParts.length - 1];
    const parentPath = pathParts.slice(0, -1).join('.');

    setFields((prev) => {
      const newFields = { ...prev };

      if (parentPath) {
        // Updating nested field
        const updateNestedField = (
          obj: Record<string, TemplateField>,
          pathArray: string[],
          fieldToUpdate: string,
          updates: Partial<TemplateField>,
        ) => {
          if (pathArray.length === 0) {
            obj[fieldToUpdate] = { ...obj[fieldToUpdate], ...updates };
          } else {
            const [first, ...rest] = pathArray;
            if (first.endsWith('[]')) {
              const arrayField = first.slice(0, -2);
              if (obj[arrayField]?.items?.properties) {
                updateNestedField(
                  obj[arrayField].items.properties!,
                  rest,
                  fieldToUpdate,
                  updates,
                );
              }
            } else {
              if (obj[first]?.properties) {
                updateNestedField(
                  obj[first].properties!,
                  rest,
                  fieldToUpdate,
                  updates,
                );
              }
            }
          }
        };

        updateNestedField(newFields, parentPath.split('.'), fieldName, changes);
      } else {
        // Updating top-level field
        newFields[fieldName] = { ...newFields[fieldName], ...changes };
      }

      return newFields;
    });
  };

  const onFieldNameChange = (oldPath: string, newName: string) => {
    const pathParts = oldPath.split('.');
    const oldFieldName = pathParts[pathParts.length - 1];
    const parentPath = pathParts.slice(0, -1).join('.');

    if (oldFieldName === newName) return; // No change needed

    const newPath = parentPath ? `${parentPath}.${newName}` : newName;

    setFields((prev) => {
      const newFields = { ...prev };

      if (parentPath) {
        // Renaming nested field
        const renameNestedField = (
          obj: Record<string, TemplateField>,
          pathArray: string[],
          oldName: string,
          newName: string,
        ) => {
          if (pathArray.length === 0) {
            if (obj[oldName] && !obj[newName]) {
              // Preserve order by recreating object with same key order
              const entries = Object.entries(obj);
              const newObj: Record<string, TemplateField> = {};

              for (const [key, value] of entries) {
                if (key === oldName) {
                  newObj[newName] = value; // Use new name but same position
                } else {
                  newObj[key] = value;
                }
              }

              // Replace the object's contents
              Object.keys(obj).forEach((key) => delete obj[key]);
              Object.assign(obj, newObj);
            }
          } else {
            const [first, ...rest] = pathArray;
            if (first.endsWith('[]')) {
              const arrayField = first.slice(0, -2);
              if (obj[arrayField]?.items?.properties) {
                renameNestedField(
                  obj[arrayField].items.properties!,
                  rest,
                  oldName,
                  newName,
                );
              }
            } else {
              if (obj[first]?.properties) {
                renameNestedField(
                  obj[first].properties!,
                  rest,
                  oldName,
                  newName,
                );
              }
            }
          }
        };

        renameNestedField(
          newFields,
          parentPath.split('.'),
          oldFieldName,
          newName,
        );
      } else {
        // Renaming top-level field - preserve order
        if (newFields[oldFieldName] && !newFields[newName]) {
          const entries = Object.entries(newFields);
          const newObj: Record<string, TemplateField> = {};

          for (const [key, value] of entries) {
            if (key === oldFieldName) {
              newObj[newName] = value; // Use new name but same position
            } else {
              newObj[key] = value;
            }
          }

          return newObj; // Return the new object with preserved order
        }
      }

      return newFields;
    });

    // Update expanded fields set to reflect the new path
    setExpandedFields((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(oldPath)) {
        newSet.delete(oldPath);
        newSet.add(newPath);
      }
      return newSet;
    });

    // Update field ID mapping to use new path
    setFieldIds((prev) => {
      const newMap = new Map(prev);

      // Move the stable ID from old path to new path
      if (newMap.has(oldPath)) {
        const stableId = newMap.get(oldPath)!;
        newMap.delete(oldPath);
        newMap.set(newPath, stableId);
      }

      // Update any child paths that start with the old path
      const updates: Array<[string, string, string]> = []; // [oldPath, newPath, id]
      for (const [path, id] of newMap) {
        if (path.startsWith(oldPath + '.')) {
          const suffix = path.substring(oldPath.length);
          const updatedPath = newPath + suffix;
          updates.push([path, updatedPath, id]);
        } else if (path.startsWith(oldPath + '[]')) {
          const suffix = path.substring(oldPath.length);
          const updatedPath = newPath + suffix;
          updates.push([path, updatedPath, id]);
        }
      }

      // Apply updates
      for (const [oldPath, newPath, id] of updates) {
        newMap.delete(oldPath);
        newMap.set(newPath, id);
      }

      return newMap;
    });
  };

  const resetForm = () => {
    setName('');
    setDescription('');
    setFields({});
    setFieldIds(new Map());
    setExpandedFields(new Set());
    setNewField(DEFAULT_NEW_FIELD);
  };

  return {
    // State
    name,
    setName,
    description,
    setDescription,
    fields,
    setFields,
    expandedFields,
    newField,
    setNewField,

    // Computed
    fieldRows: flattenFields(fields),

    // Actions
    toggleExpanded,
    deleteField,
    addField,
    onFieldChange,
    onFieldNameChange,
    resetForm,
  };
}
