'use client';

import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  type AlertResult,
  type AlertType,
  useOneDialog,
} from '@/hooks/use-one-dialog';

export function OneDialog() {
  const { dialog, open, dispatch } = useOneDialog();

  const handleClose = (result?: AlertResult<AlertType>) => {
    if (dialog) {
      const value: unknown = dialog.type === 'alert' ? true : (result ?? false);
      if (dialog.resolver) {
        dialog.resolver(value as AlertResult<AlertType>);
        delete dialog.resolver;
      }
      dispatch();
    }
  };

  return (
    <AlertDialog
      open={!!open}
      onOpenChange={(opened: boolean) => {
        if (!opened) handleClose();
      }}
    >
      <AlertDialogContent
        asChild
        onFocusOutside={(e: Event) => e.preventDefault()}
      >
        <form
          onSubmit={(event) => {
            event.preventDefault();
            handleClose(
              dialog?.type === 'prompt'
                ? event.currentTarget.prompt?.value || ''
                : true,
            );
          }}
        >
          <AlertDialogHeader>
            <AlertDialogTitle>{dialog?.title ?? ''}</AlertDialogTitle>
            {dialog?.body && (
              <AlertDialogDescription>{dialog?.body}</AlertDialogDescription>
            )}
          </AlertDialogHeader>
          {dialog?.type === 'prompt' && (
            <Input name="prompt" defaultValue={dialog?.defaultValue} />
          )}
          <AlertDialogFooter>
            <Button
              type="button"
              variant={dialog?.type === 'alert' ? 'default' : 'outline'}
              className="min-w-20"
              onClick={() => handleClose()}
            >
              {dialog?.closeButton}
            </Button>
            {dialog?.type !== 'alert' && (
              <Button
                type="submit"
                variant={dialog?.actionButtonVariant || 'default'}
                className="min-w-20"
              >
                {dialog?.actionButton}
              </Button>
            )}
          </AlertDialogFooter>
        </form>
      </AlertDialogContent>
    </AlertDialog>
  );
}
