import { Logo } from '../logo';

export function WebFooter() {
  return (
    <footer className="bg-gray-900 text-white py-12" role="contentinfo">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div
                className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center"
                aria-hidden="true"
              >
                <Logo className="w-5 h-5 text-white" aria-hidden="true" />
              </div>
              <span className="text-xl font-bold">Unfurl</span>
            </div>
            <p className="text-gray-400">
              AI-powered data extraction for modern teams.
            </p>
          </div>
          <nav aria-labelledby="product-links">
            <h3 id="product-links" className="font-semibold mb-4">
              Product
            </h3>
            <ul className="space-y-2 text-gray-400">
              <li>
                <a
                  href="#features"
                  className="hover:text-white transition-colors"
                >
                  Features
                </a>
              </li>
              <li>
                <a
                  href="/pricing"
                  className="hover:text-white transition-colors"
                >
                  Pricing
                </a>
              </li>
              <li>
                <a
                  href="/api-docs"
                  className="hover:text-white transition-colors"
                >
                  API
                </a>
              </li>
              <li>
                <a
                  href="/integrations"
                  className="hover:text-white transition-colors"
                >
                  Integrations
                </a>
              </li>
            </ul>
          </nav>
          <nav aria-labelledby="company-links">
            <h3 id="company-links" className="font-semibold mb-4">
              Company
            </h3>
            <ul className="space-y-2 text-gray-400">
              <li>
                <a href="/about" className="hover:text-white transition-colors">
                  About
                </a>
              </li>
              <li>
                <a href="/blog" className="hover:text-white transition-colors">
                  Blog
                </a>
              </li>
              <li>
                <a
                  href="/careers"
                  className="hover:text-white transition-colors"
                >
                  Careers
                </a>
              </li>
              <li>
                <a
                  href="/contact"
                  className="hover:text-white transition-colors"
                >
                  Contact
                </a>
              </li>
            </ul>
          </nav>
          <nav aria-labelledby="support-links">
            <h3 id="support-links" className="font-semibold mb-4">
              Support
            </h3>
            <ul className="space-y-2 text-gray-400">
              <li>
                <a
                  href="/api-docs"
                  className="hover:text-white transition-colors"
                >
                  Documentation
                </a>
              </li>
              <li>
                <a href="/help" className="hover:text-white transition-colors">
                  Help Center
                </a>
              </li>
              <li>
                <a
                  href="/status"
                  className="hover:text-white transition-colors"
                >
                  Status
                </a>
              </li>
              <li>
                <a
                  href="/privacy"
                  className="hover:text-white transition-colors"
                >
                  Privacy
                </a>
              </li>
            </ul>
          </nav>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} Unfurl. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
