'use client';

import { useUser } from '@/contexts/user-context';
import { But<PERSON> } from '../ui/button';
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

export function GetStartedButton({
  className,
  ...props
}: React.ComponentProps<'button'>) {
  const { user } = useUser();

  return user ? (
    <Button
      asChild
      size="lg"
      aria-label="Start using Unfurl for free"
      className={cn('px-8 py-4 text-lg rounded-lg', className)}
      {...props}
    >
      <Link href="/dashboard">
        Go to Dashboard
        <ArrowRight className="ml-2 w-5 h-5" aria-hidden="true" />
      </Link>
    </Button>
  ) : (
    <Button
      asChild
      size="lg"
      aria-label="Start using Unfurl for free"
      className={cn('px-8 py-4 text-lg rounded-lg', className)}
      {...props}
    >
      <Link href="/register">
        Get Started for Free
        <ArrowRight className="ml-2 w-5 h-5" aria-hidden="true" />
      </Link>
    </Button>
  );
}
