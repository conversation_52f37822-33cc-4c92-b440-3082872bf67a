import { LoginOrDashboardButton } from './login-or-dashboard-button';
import { Logo } from '../logo';

export function WebHeader() {
  return (
    <nav
      className="border-b border-gray-100 bg-white/80 backdrop-blur-sm sticky top-0 z-50"
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-2">
            <div
              className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center"
              aria-hidden="true"
            >
              <Logo className="w-5 h-5 text-white" aria-hidden="true" />
            </div>
            <span className="text-xl font-bold text-primary">Unfurl</span>
          </div>
          <div className="hidden md:flex items-center space-x-8">
            <a
              href="#features"
              className="text-gray-600 hover:text-primary transition-colors"
              aria-label="Navigate to features section"
            >
              Features
            </a>
            <a
              href="/pricing"
              className="text-gray-600 hover:text-primary transition-colors"
              aria-label="Navigate to pricing section"
            >
              Pricing
            </a>
            <a
              href="/api-docs"
              className="text-gray-600 hover:text-primary transition-colors"
              aria-label="Navigate to documentation"
            >
              Docs
            </a>
            <LoginOrDashboardButton />
          </div>
        </div>
      </div>
    </nav>
  );
}
