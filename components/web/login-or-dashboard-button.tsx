'use client';

import { useUser } from '@/contexts/user-context';
import { But<PERSON> } from '../ui/button';
import Link from 'next/link';

export function LoginOrDashboardButton() {
  const { user } = useUser();

  return user ? (
    <Button asChild variant="outline" aria-label="Go to dashboard">
      <Link href="/dashboard">Dashboard</Link>
    </Button>
  ) : (
    <Button asChild variant="outline" aria-label="Log in to your account">
      <Link href="/login">Log In</Link>
    </Button>
  );
}
