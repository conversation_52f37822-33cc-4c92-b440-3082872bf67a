'use client';

import { createContext, useContext } from 'react';
import type { User } from '@/lib/db';
import useSWR from 'swr';

interface UserContextType {
  user?: User | null;
  loading: boolean;
  error: Error | null;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

const fetcher = async (url: string): Promise<User> => {
  const response = await fetch(url);
  const result = await response.json();

  if (!result.success) {
    throw new Error(result.error || 'Failed to fetch API keys');
  }

  return result.data;
};

export function UserProvider({ children }: { children: React.ReactNode }) {
  const { data: user, error, isLoading } = useSWR('/api/user', fetcher);

  return (
    <UserContext.Provider value={{ user, loading: isLoading, error }}>
      {children}
    </UserContext.Provider>
  );
}

export function useUser() {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
}
