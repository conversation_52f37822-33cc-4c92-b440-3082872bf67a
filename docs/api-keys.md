# API Keys Documentation

## Overview

API keys provide programmatic access to Unfurl's document processing capabilities. With an API key, you can integrate Unfurl's data extraction features directly into your applications.

## Creating API Keys

### Via Dashboard

1. Navigate to the **API Keys** page in your Unfurl dashboard
2. Click **Create API Key**
3. Enter a descriptive name for your key (e.g., "Production Integration", "Development Testing")
4. Click **Create API Key**
5. **Important**: Copy your API key immediately - you won't be able to see it again

### Limits

- Maximum of 10 API keys per user
- API key names must be 50 characters or less
- Keys can be deleted but not regenerated

## Authentication

Include your API key in the `Authorization` header of your requests:

```bash
Authorization: Bearer your_api_key_here
```

## Usage Examples

### Basic Document Upload

```bash
curl -X POST https://your-unfurl-instance.com/api/documents/upload \
  -H "Authorization: Bearer unfurl_your_api_key_here" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/document.pdf" \
  -F "templateId=template_id_here"
```

### JavaScript/Node.js Example

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('templateId', 'your_template_id');

const response = await fetch('/api/documents/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer unfurl_your_api_key_here'
  },
  body: formData
});

const result = await response.json();
```

### Python Example

```python
import requests

headers = {
    'Authorization': 'Bearer unfurl_your_api_key_here'
}

files = {
    'file': open('/path/to/document.pdf', 'rb'),
    'templateId': (None, 'your_template_id')
}

response = requests.post(
    'https://your-unfurl-instance.com/api/documents/upload',
    headers=headers,
    files=files
)

result = response.json()
```

## API Endpoints

### Document Processing

- `POST /api/documents/upload` - Upload and process a document
- `GET /api/documents/{id}` - Get document details
- `GET /api/documents/{id}/data` - Get extracted data
- `DELETE /api/documents/{id}` - Delete a document

### Templates

- `GET /api/templates` - List available templates
- `GET /api/templates/{id}` - Get template details
- `POST /api/templates` - Create a new template
- `PUT /api/templates/{id}` - Update a template
- `DELETE /api/templates/{id}` - Delete a template

### User & Account

- `GET /api/user` - Get current user information
- `GET /api/user/credits` - Get credit balance and usage

## Rate Limits

API requests are subject to the following limits:

- **Free Plan**: 100 requests per hour
- **Starter Plan**: 1,000 requests per hour
- **Professional Plan**: 10,000 requests per hour
- **Enterprise Plan**: Custom limits

Rate limit headers are included in all API responses:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Error Handling

### Common Error Codes

- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Invalid or missing API key
- `403 Forbidden` - Insufficient permissions or credits
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

### Error Response Format

```json
{
  "error": "Invalid API key",
  "code": "UNAUTHORIZED",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Security Best Practices

### Storage

- **Never** hardcode API keys in your source code
- Store API keys as environment variables
- Use secure key management services in production
- Rotate keys regularly

### Usage

- Use HTTPS for all API requests
- Implement proper error handling
- Log API usage for monitoring
- Set up alerts for unusual activity

### Example Environment Setup

```bash
# .env file
UNFURL_API_KEY=unfurl_your_api_key_here
UNFURL_API_BASE_URL=https://your-unfurl-instance.com
```

```javascript
// In your application
const apiKey = process.env.UNFURL_API_KEY;
const baseUrl = process.env.UNFURL_API_BASE_URL;

if (!apiKey) {
  throw new Error('UNFURL_API_KEY environment variable is required');
}
```

## Monitoring Usage

### Dashboard

- View request counts and usage patterns in the API Keys dashboard
- Monitor credit consumption
- Track last usage timestamps

### API Endpoints

```bash
# Get API key usage statistics
curl -X GET https://your-unfurl-instance.com/api/user/usage \
  -H "Authorization: Bearer unfurl_your_api_key_here"
```

## Webhooks (Coming Soon)

Configure webhooks to receive notifications when document processing is complete:

```json
{
  "webhook_url": "https://your-app.com/webhooks/unfurl",
  "events": ["document.processed", "extraction.completed"]
}
```

## SDKs and Libraries

### Official SDKs

- JavaScript/TypeScript SDK (Coming Soon)
- Python SDK (Coming Soon)
- Go SDK (Coming Soon)

### Community Libraries

Check our [GitHub organization](https://github.com/unfurl) for community-contributed libraries and examples.

## Support

For API-related questions and support:

- Documentation: [docs.unfurl.ink](https://docs.unfurl.ink)
- GitHub Issues: [github.com/unfurl/api-issues](https://github.com/unfurl/api-issues)
- Email: <EMAIL>
- Discord: [discord.gg/unfurl](https://discord.gg/unfurl)

## Changelog

### v1.0.0 (Current)
- Initial API release
- Document upload and processing
- Template management
- User account endpoints
- Rate limiting
- API key authentication

---

*Last updated: January 2024*