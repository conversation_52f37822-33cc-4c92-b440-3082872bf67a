# Unfurl API v1 Documentation

## Overview

The Unfurl API v1 provides programmatic access to document processing, data extraction, and management capabilities. This RESTful API uses API key authentication and returns JSON responses.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

All API endpoints (except the health check) require authentication using an API key. You can obtain an API key from your dashboard.

### Authentication Methods

**Option 1: X-API-Key Header**
```http
X-API-Key: your-api-key-here
```

**Option 2: Authorization Bearer Header**
```http
Authorization: Bearer your-api-key-here
```

## Response Format

All API responses follow a consistent JSON format:

```json
{
  "success": true|false,
  "data": {}, // Present on successful requests
  "error": "Error message", // Present on failed requests
  "message": "Success message" // Present on some successful operations
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 400  | Bad Request - Invalid parameters or request body |
| 401  | Unauthorized - Invalid or missing API key |
| 402  | Payment Required - Insufficient credits |
| 404  | Not Found - Resource not found |
| 409  | Conflict - Resource already exists |
| 429  | Too Many Requests - Rate limit exceeded |
| 500  | Internal Server Error |
| 503  | Service Unavailable - API unhealthy |

## Rate Limiting

- **Default Limit**: 1000 requests per minute per API key
- **Rate limit headers** are included in responses:
  - `X-RateLimit-Limit`: Maximum requests per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets

## API Endpoints

### Health Check

#### GET /api/v1/

Get API status and health information. No authentication required.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "uptime": 12345,
    "database": "connected",
    "features": ["Document Upload", "Document Management", "..."],
    "endpoints": [...]
  }
}
```

---

### User Information

#### GET /api/v1/user

Get current user information and usage statistics.

**Headers:**
```http
X-API-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "User Name",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "subscriptionPlan": "FREE",
      "creditBalance": 10,
      "monthlyCreditsLimit": 10
    },
    "statistics": {
      "totalDocuments": 25,
      "totalJobs": 30,
      "completedJobs": 28,
      "totalExtractedData": 25
    },
    "usage": {
      "documentsThisMonth": 5,
      "creditsUsedThisMonth": 2
    }
  }
}
```

#### PUT /api/v1/user

Update user profile information.

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "name": "New Name"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-id",
      "name": "New Name",
      "email": "<EMAIL>"
    }
  }
}
```

---

### Document Upload

#### POST /api/v1/upload

Upload a document for processing. This endpoint generates a presigned URL for direct upload to cloud storage and creates the necessary database records.

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "filename": "document.pdf",
  "contentType": "application/pdf",
  "fileSize": 1024000,
  "templateId": "optional-template-id"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "presignedUrl": "https://s3.amazonaws.com/bucket/upload-url",
    "fileKey": "unique-file-key",
    "uploadUrl": "https://s3.amazonaws.com/bucket/upload-url",
    "jobId": "job-id",
    "documentId": "document-id"
  }
}
```

**Upload Process:**
1. Call this endpoint to get a presigned URL
2. Use the presigned URL to upload your file directly to cloud storage
3. The document will be automatically processed

**Supported File Types:**
- PDF: `application/pdf`
- Images: `image/png`, `image/jpeg`, `image/jpg`

**File Size Limits:**
- Maximum: 10MB per file

---

### Document Management

#### GET /api/v1/documents

List all documents for the authenticated user.

**Headers:**
```http
X-API-Key: your-api-key
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 50, max: 100)
- `offset` (optional): Pagination offset (default: 0)
- `status` (optional): Filter by status (`UPLOADED`, `PROCESSING`, `COMPLETED`, `FAILED`)

**Response:**
```json
{
  "success": true,
  "data": {
    "documents": [
      {
        "id": "doc-id",
        "originalName": "document.pdf",
        "filename": "unique-filename",
        "mimeType": "application/pdf",
        "size": 1024000,
        "status": "COMPLETED",
        "uploadedAt": "2024-01-01T00:00:00.000Z",
        "storageUrl": "https://...",
        "template": {
          "id": "template-id",
          "name": "Template Name"
        },
        "jobs": [
          {
            "id": "job-id",
            "status": "COMPLETED",
            "type": "EXTRACT"
          }
        ]
      }
    ],
    "total": 123,
    "limit": 50,
    "offset": 0
  }
}
```

#### POST /api/v1/documents

Create a document record (alternative to upload endpoint).

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "fileKey": "unique-file-key-from-upload",
  "filename": "original-filename.pdf",
  "contentType": "application/pdf",
  "fileSize": 1024000,
  "templateId": "optional-template-id"
}
```

#### GET /api/v1/documents/{id}

Get a specific document by ID.

**Headers:**
```http
X-API-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "data": {
    "document": {
      "id": "doc-id",
      "originalName": "document.pdf",
      "status": "COMPLETED",
      "template": {...},
      "jobs": [...],
      "extractedData": [...]
    }
  }
}
```

#### PUT /api/v1/documents/{id}

Update a document's metadata.

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "originalName": "new-filename.pdf",
  "templateId": "template-id"
}
```

#### DELETE /api/v1/documents/{id}

Delete a document and all associated data.

**Headers:**
```http
X-API-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "message": "Document deleted successfully"
}
```

---

### Extracted Data

#### GET /api/v1/documents/{id}/data

Get extracted data for a specific document.

**Headers:**
```http
X-API-Key: your-api-key
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 10, max: 50)
- `offset` (optional): Pagination offset (default: 0)
- `validated` (optional): Filter by validation status (`true`/`false`)

**Response:**
```json
{
  "success": true,
  "data": {
    "extractedData": [
      {
        "id": "extracted-data-id",
        "data": {
          "field1": "value1",
          "field2": "value2"
        },
        "confidence": 0.95,
        "isValidated": true,
        "validatedAt": "2024-01-01T00:00:00.000Z",
        "createdAt": "2024-01-01T00:00:00.000Z",
        "job": {
          "id": "job-id",
          "type": "EXTRACT",
          "status": "COMPLETED"
        }
      }
    ],
    "total": 5,
    "limit": 10,
    "offset": 0
  }
}
```

#### POST /api/v1/documents/{id}/data

Validate or update extracted data.

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "extractedDataId": "extracted-data-id",
  "isValidated": true,
  "correctedData": {
    "field1": "corrected-value1",
    "field2": "corrected-value2"
  }
}
```

---

### Job Management

#### GET /api/v1/jobs

List all jobs for the authenticated user.

**Headers:**
```http
X-API-Key: your-api-key
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 50, max: 100)
- `offset` (optional): Pagination offset (default: 0)
- `status` (optional): Filter by status (`PENDING`, `RUNNING`, `COMPLETED`, `FAILED`, `CANCELLED`)
- `type` (optional): Filter by type (`EXTRACT`, `VALIDATE`, `EXPORT`)
- `documentId` (optional): Filter by document ID

**Response:**
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "id": "job-id",
        "status": "COMPLETED",
        "type": "EXTRACT",
        "progress": 100,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "completedAt": "2024-01-01T00:05:00.000Z",
        "document": {
          "id": "doc-id",
          "originalName": "document.pdf"
        },
        "extractedData": [...]
      }
    ],
    "total": 50,
    "limit": 50,
    "offset": 0
  }
}
```

#### POST /api/v1/jobs

Create a new job for processing a document.

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "documentId": "document-id",
  "type": "EXTRACT",
  "templateId": "optional-template-id"
}
```

#### GET /api/v1/jobs/{id}

Get detailed information about a specific job.

**Headers:**
```http
X-API-Key: your-api-key
```

#### PUT /api/v1/jobs/{id}

Update a job's status (primarily for cancellation).

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "status": "CANCELLED"
}
```

#### DELETE /api/v1/jobs/{id}

Cancel and delete a job.

**Headers:**
```http
X-API-Key: your-api-key
```

---

### Template Management

#### GET /api/v1/templates

List available templates (public + user's custom templates).

**Headers:**
```http
X-API-Key: your-api-key
```

**Query Parameters:**
- `limit` (optional): Number of results (default: 50, max: 100)
- `offset` (optional): Pagination offset (default: 0)
- `isPublic` (optional): Filter by public templates (`true`/`false`)
- `search` (optional): Search by name or description

**Response:**
```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "template-id",
        "name": "Invoice Template",
        "description": "Extract data from invoices",
        "isPublic": true,
        "fields": {
          "invoiceNumber": {
            "type": "text",
            "label": "Invoice Number",
            "required": true
          },
          "amount": {
            "type": "number",
            "label": "Total Amount",
            "required": true
          }
        },
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "total": 10,
    "limit": 50,
    "offset": 0
  }
}
```

#### POST /api/v1/templates

Create a custom template.

**Headers:**
```http
X-API-Key: your-api-key
Content-Type: application/json
```

**Body:**
```json
{
  "name": "My Custom Template",
  "description": "Description of the template",
  "fields": {
    "field1": {
      "type": "text",
      "label": "Field 1",
      "required": true
    },
    "field2": {
      "type": "number",
      "label": "Field 2",
      "required": false
    }
  }
}
```

**Supported Field Types:**
- `text`: Text input
- `number`: Numeric input
- `date`: Date input
- `boolean`: Boolean checkbox
- `select`: Dropdown selection
- `textarea`: Multi-line text

---

## Error Handling

### Common Error Responses

**Authentication Error (401):**
```json
{
  "success": false,
  "error": "API key required. Provide via X-API-Key header or Authorization: Bearer <key>"
}
```

**Insufficient Credits (402):**
```json
{
  "success": false,
  "error": "Insufficient credits. Required: 1, Available: 0"
}
```

**Validation Error (400):**
```json
{
  "success": false,
  "error": "Missing required fields: filename, contentType, fileSize"
}
```

**Not Found (404):**
```json
{
  "success": false,
  "error": "Document not found"
}
```

**Rate Limit (429):**
```json
{
  "success": false,
  "error": "Rate limit exceeded. Try again in 60 seconds."
}
```

---

## Code Examples

### JavaScript/Node.js

```javascript
const apiKey = 'your-api-key';
const baseUrl = 'https://your-domain.com/api/v1';

// Upload a document
async function uploadDocument(file) {
  // Step 1: Get presigned URL
  const uploadResponse = await fetch(`${baseUrl}/upload`, {
    method: 'POST',
    headers: {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      filename: file.name,
      contentType: file.type,
      fileSize: file.size,
    }),
  });

  const { data } = await uploadResponse.json();

  // Step 2: Upload file to presigned URL
  await fetch(data.presignedUrl, {
    method: 'PUT',
    body: file,
    headers: {
      'Content-Type': file.type,
    },
  });

  return data.documentId;
}

// Get document status
async function getDocument(documentId) {
  const response = await fetch(`${baseUrl}/documents/${documentId}`, {
    headers: {
      'X-API-Key': apiKey,
    },
  });

  return await response.json();
}

// Get extracted data
async function getExtractedData(documentId) {
  const response = await fetch(`${baseUrl}/documents/${documentId}/data`, {
    headers: {
      'X-API-Key': apiKey,
    },
  });

  return await response.json();
}
```

### Python

```python
import requests

api_key = 'your-api-key'
base_url = 'https://your-domain.com/api/v1'
headers = {'X-API-Key': api_key}

# Upload a document
def upload_document(file_path, filename, content_type):
    # Step 1: Get presigned URL
    with open(file_path, 'rb') as f:
        file_size = len(f.read())

    upload_response = requests.post(
        f'{base_url}/upload',
        headers={**headers, 'Content-Type': 'application/json'},
        json={
            'filename': filename,
            'contentType': content_type,
            'fileSize': file_size
        }
    )

    data = upload_response.json()['data']

    # Step 2: Upload file to presigned URL
    with open(file_path, 'rb') as f:
        requests.put(
            data['presignedUrl'],
            data=f,
            headers={'Content-Type': content_type}
        )

    return data['documentId']

# Get document status
def get_document(document_id):
    response = requests.get(
        f'{base_url}/documents/{document_id}',
        headers=headers
    )
    return response.json()

# List all documents
def list_documents(limit=50, offset=0):
    response = requests.get(
        f'{base_url}/documents',
        headers=headers,
        params={'limit': limit, 'offset': offset}
    )
    return response.json()
```

### cURL

```bash
# Health check
curl -X GET https://your-domain.com/api/v1/

# Get user info
curl -X GET https://your-domain.com/api/v1/user \
  -H "X-API-Key: your-api-key"

# Upload document (get presigned URL)
curl -X POST https://your-domain.com/api/v1/upload \
  -H "X-API-Key: your-api-key" \
  -H "Content-Type: application/json" \
  -d '{
    "filename": "document.pdf",
    "contentType": "application/pdf",
    "fileSize": 1024000
  }'

# List documents
curl -X GET "https://your-domain.com/api/v1/documents?limit=10&offset=0" \
  -H "X-API-Key: your-api-key"

# Get specific document
curl -X GET https://your-domain.com/api/v1/documents/doc-id \
  -H "X-API-Key: your-api-key"

# Get extracted data
curl -X GET https://your-domain.com/api/v1/documents/doc-id/data \
  -H "X-API-Key: your-api-key"

# List jobs
curl -X GET "https://your-domain.com/api/v1/jobs?status=COMPLETED" \
  -H "X-API-Key: your-api-key"
```

---

## Webhooks (Coming Soon)

Webhook support for real-time notifications about job completion, document processing, and other events will be available in a future release.

---

## Support

- **Documentation**: https://docs.unfurl.ink/api/v1
- **Support Email**: <EMAIL>
- **API Status**: https://status.unfurl.ink

---

## Changelog

### v1.0.0 (2024-01-01)
- Initial API release
- Document upload and management
- Job processing and monitoring
- Template management
- User information and statistics
- API key authentication
- Rate limiting