'use client';

import * as React from 'react';

export type AlertType = 'alert' | 'confirm' | 'prompt';
export type AlertResult<T extends AlertType> = T extends 'prompt'
  ? string | false
  : T extends 'confirm'
    ? boolean
    : true;

export type AlertRequest<T extends AlertType> = T extends 'alert'
  ? {
      title?: React.ReactNode;
      body: React.ReactNode;
      closeButton?: React.ReactNode;
    }
  : T extends 'confirm'
    ? {
        title?: React.ReactNode;
        body: React.ReactNode;
        closeButton?: React.ReactNode;
        actionButton?: React.ReactNode;
        actionButtonVariant?: 'default' | 'secondary' | 'destructive';
      }
    : T extends 'prompt'
      ? {
          title?: React.ReactNode;
          body: React.ReactNode;
          closeButton?: React.ReactNode;
          actionButton?: React.ReactNode;
          actionButtonVariant?: 'default' | 'secondary' | 'destructive';
          defaultValue?: string;
        }
      : never;

interface AlertDialogState<T extends AlertType> {
  title?: React.ReactNode;
  body: React.ReactNode;
  type: T;
  closeButton: React.ReactNode;
  actionButton: React.ReactNode;
  actionButtonVariant?: 'default' | 'secondary' | 'destructive';
  defaultValue?: string;
  resolver?: (value: AlertResult<T>) => void;
}

const listeners: Array<(state: typeof memoryState) => void> = [];
let memoryState: { dialog: AlertDialogState<AlertType>; open?: true } =
  {} as typeof memoryState;

const dispatch = <T extends AlertType>(dialog?: AlertDialogState<T>) => {
  if (memoryState.dialog) {
    const { dialog } = memoryState;
    if (dialog?.resolver) {
      dialog.resolver((dialog.type === 'alert') as unknown as AlertResult<T>);
      delete dialog.resolver;
    }
  }
  memoryState = dialog
    ? { dialog, open: true }
    : { dialog: memoryState.dialog };
  listeners.forEach((listener) => listener(memoryState));
};

const promiser = <T>() => {
  let resolve: unknown, reject: unknown;
  const promise = new Promise<T>((ok, no) => {
    resolve = ok;
    reject = no;
  });
  return {
    promise,
    resolve: resolve as (value: T | PromiseLike<T>) => void,
    reject: reject as (reason?: any) => void,
  };
};

function subscribe(listener: () => void) {
  listeners.push(listener);
  return () => {
    const i = listeners.indexOf(listener);
    if (i > -1) {
      listeners.splice(i, 1);
    }
  };
}

function getSnapshot() {
  return memoryState;
}

export function useOneDialog() {
  const state = React.useSyncExternalStore(
    subscribe,
    getSnapshot,
    () => ({}) as typeof memoryState,
  );
  return { ...state, dispatch };
}

export function alert(params: AlertRequest<'alert'>) {
  const { promise, resolve } = promiser<AlertResult<'alert'>>();
  dispatch({
    ...params,
    type: 'alert',
    closeButton: params.closeButton ?? 'Close',
    actionButton: '',
    resolver: resolve,
  });
  return promise;
}

export function confirm(params: AlertRequest<'confirm'>) {
  const { promise, resolve } = promiser<AlertResult<'confirm'>>();
  dispatch({
    ...params,
    type: 'confirm',
    closeButton: params.closeButton ?? 'Close',
    actionButton: params.actionButton ?? 'Confirm',
    resolver: resolve,
  });
  return promise;
}

export function prompt(params: AlertRequest<'prompt'>) {
  const { promise, resolve } = promiser<AlertResult<'prompt'>>();
  dispatch({
    ...params,
    type: 'prompt',
    closeButton: params.closeButton ?? 'Close',
    actionButton: params.actionButton ?? 'Confirm',
    resolver: resolve,
  });
  return promise;
}
