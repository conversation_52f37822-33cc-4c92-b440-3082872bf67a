'use client';

import { useState } from 'react';
import { createPresignedUrlAndJob } from '@/lib/actions/upload';
import { updateJobStatus } from '@/lib/actions/jobs';
import { JobStatus } from '@/lib/db/jobs';

export const useUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadFile = async (file: File, templateId: string) => {
    setIsUploading(true);
    setError(null);

    try {
      // Step 1: Call the server action to get a presigned URL and create a job record
      const { success, presignedUrl, jobId } = await createPresignedUrlAndJob({
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
        templateId,
      });

      if (!success) {
        throw new Error('Failed to get presigned URL from server.');
      }

      // Step 2: Upload the file directly to S3 using the presigned URL
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to upload file to S3.');
      }

      // Step 3: Notify our backend that the upload is complete, so it can be queued for processing.
      await updateJobStatus(jobId, JobStatus.PENDING);

      console.log('Upload successful! Job is now pending processing.');
      return { success: true, jobId };
    } catch (err: any) {
      console.error('Upload failed:', err);
      setError(err.message || 'An unknown error occurred.');
      return { success: false, jobId: null };
    } finally {
      setIsUploading(false);
    }
  };

  return { uploadFile, isUploading, error };
};
