'use server';

import { requireUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { revalidatePath } from 'next/cache';
import { jobOperations, JobStatus } from '@/lib/db/jobs';
import { queue } from '@/lib/queue';

export async function addJobToQueue(jobId: string) {
  await queue.enqueueJSON({
    url: `${process.env.NEXT_PUBLIC_APP_URL}/api/worker/process-job`,
    body: {
      jobId: jobId,
    },
    headers: {
      Authorization: `Bearer ${process.env.WORKER_SECRET!}`,
    },
  });
}

export async function updateJobStatus(
  jobId: string,
  status: JobStatus,
  error?: string,
) {
  const user = requireUser();

  // Ensure the user owns the job they are trying to update
  const job = await prisma.job.findFirst({
    where: {
      id: jobId,
      userId: user.id,
    },
  });

  if (!job) {
    throw new Error('Job not found or user is not authorized.');
  }

  await jobOperations.updateStatus(jobId, status, error);

  if (status === JobStatus.PENDING) {
    await addJobToQueue(jobId);
  }

  // Revalidate the jobs path to show the new job status
  revalidatePath('/jobs');
}
