'use server';

import { requireUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import {
  generateFileKey,
  generatePresignedUploadUrl,
  getFileUrl,
  validateFile,
} from '@/lib/s3';
import { checkCredits } from '../utils';

export async function createPresignedUrlAndJob({
  fileName,
  fileType,
  fileSize,
  templateId,
}: {
  fileName: string;
  fileType: string;
  fileSize: number;
  templateId: string;
}) {
  // Authenticate user and get database user
  const user = await requireUser();

  checkCredits(user);

  // Validate file
  validateFile({ type: fileType, size: fileSize });

  // Create a Job record in the database first
  // This gives us a unique jobId to use in the S3 key.
  const newJob = await prisma.job.create({
    data: {
      userId: user!.id,
      fileName,
      status: 'UPLOADING', // A temporary status while client uploads
      fileUrl: '', // Will be updated after this step
      templateId,
    },
  });

  // Generate unique file key
  const fileKey = generateFileKey(user.id, newJob.id, fileName);

  // Update the job with the final S3 URL
  await prisma.job.update({
    where: { id: newJob.id },
    data: { fileUrl: getFileUrl(fileKey) },
  });

  // Generate presigned URL
  const presignedUrl = await generatePresignedUploadUrl(
    fileKey,
    fileType,
    fileSize,
    3600, // 1 hour expiration
  );

  // Return the URL and Job Id to the client
  return { success: true, presignedUrl, jobId: newJob.id };
}
