import { callMistralOCRAPI } from './providers/mistral-ocr';
import { callOpenAIVisionAPI } from './providers/openai-vision';

export type AIResult = {
  pageCount: number;
  extractedData: Record<string, unknown>;
  confidence?: number;
};
export type AIOptions = {
  fileUrl: string;
  templateSchema?: Record<string, unknown>;
};

const provider: 'mistral-ocr' | 'openai-vision' = 'openai-vision';

export async function processDocumentWithAI(
  options: AIOptions,
): Promise<AIResult> {
  switch (provider) {
    case 'mistral-ocr':
      return callMistralOCRAPI(options);
    case 'openai-vision':
      return callOpenAIVisionAPI(options);
    default:
      throw new Error('Unsupported AI provider');
  }
}
