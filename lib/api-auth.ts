import { NextRequest } from 'next/server';
import { apiKeyOperations } from '@/lib/db/api-keys';

/**
 * Extract API key from request headers
 * Supports both Authorization: Bearer <key> and X-API-Key: <key> formats
 */
function extractApiKey(request: NextRequest): string | null {
  // Check X-API-Key header first
  const apiKeyHeader = request.headers.get('X-API-Key');
  if (apiKeyHeader) {
    return apiKeyHeader;
  }

  // Check Authorization header
  const authHeader = request.headers.get('Authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7); // Remove 'Bearer ' prefix
  }

  return null;
}

/**
 * Validate API key and return associated user
 * @throws Error if API key is invalid or user not found
 */
export async function validateApiKey(request: NextRequest) {
  const apiKey = extractApiKey(request);

  if (!apiKey) {
    throw new Error(
      'API key required. Provide via X-API-Key header or Authorization: Bearer <key>',
    );
  }

  // Find API key in database
  const apiKeyRecord = await apiKeyOperations.findByKey(apiKey);

  if (!apiKeyRecord) {
    throw new Error('Invalid API key');
  }

  if (!apiKeyRecord.user) {
    throw new Error('User associated with API key not found');
  }

  // Update API key usage statistics
  await apiKeyOperations.updateUsage(apiKeyRecord.id);

  return {
    user: apiKeyRecord.user,
    apiKey: apiKeyRecord,
  };
}

/**
 * Require API key authentication for public API routes
 * Use this in API routes that require API key authentication
 */
export async function requireApiKeyAuth(request: NextRequest) {
  try {
    const { user, apiKey } = await validateApiKey(request);
    return { user, apiKey };
  } catch (error) {
    throw new Error(
      error instanceof Error ? error.message : 'Authentication failed',
    );
  }
}
