import { prisma } from '../prisma';
import type { APIKey } from './types';

// API Key operations
export const apiKeyOperations = {
  async create(data: { name: string; key: string; userId: string }) {
    return prisma.aPIKey.create({
      data,
    });
  },

  async findByKey(key: string) {
    return prisma.aPIKey.findUnique({
      where: { key },
      include: { user: true },
    });
  },

  async findByUser(userId: string): Promise<APIKey[]> {
    return prisma.aPIKey.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async updateUsage(keyId: string) {
    return prisma.aPIKey.update({
      where: { id: keyId },
      data: {
        lastUsedAt: new Date(),
      },
    });
  },

  async delete(keyId: string) {
    return prisma.aPIKey.delete({
      where: { id: keyId },
    });
  },
};
