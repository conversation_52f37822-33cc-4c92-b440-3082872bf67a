import { prisma } from '../prisma';

export enum JobStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

// Job operations
export const jobOperations = {
  async updateStatus(jobId: string, status: JobStatus, error?: string) {
    const updateData: Partial<{
      status: JobStatus;
      startedAt: Date;
      completedAt: Date;
      error: string;
    }> = { status };

    if (status === 'COMPLETED' || status === 'FAILED') {
      updateData.completedAt = new Date();
    }

    if (error) {
      updateData.error = error;
    }

    return prisma.job.update({
      where: { id: jobId },
      data: updateData,
    });
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        template: {
          select: {
            name: true,
          },
        },
      },
    });
  },

  async findByUserWithPagination(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      filters?: Record<string, unknown>;
    } = {},
  ) {
    const { limit = 50, offset = 0, filters = {} } = options;

    const where = {
      userId,
      ...filters,
    };

    return prisma.job.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        template: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  },

  async countByUser(userId: string, filters: Record<string, unknown> = {}) {
    const where = {
      userId,
      ...filters,
    };

    return prisma.job.count({
      where,
    });
  },

  // Calculate credits required for document processing
  calculateCreditsRequired(pageCount: number): number {
    // Flat rate: 1 credit = 1 page
    return pageCount;
  },

  // Helper to get page count from job record
  async getPageCount(jobId: string): Promise<number> {
    const job = await prisma.job.findUnique({
      where: { id: jobId },
      select: { pageCount: true },
    });

    return job?.pageCount || 1;
  },

  async findById(jobId: string) {
    return prisma.job.findUnique({
      where: { id: jobId },
      include: {
        user: true,
        template: true,
      },
    });
  },

  async updatePageCount(jobId: string, pageCount: number, creditsUsed: number) {
    return prisma.job.update({
      where: { id: jobId },
      data: {
        pageCount,
        creditsUsed,
      },
    });
  },
};
