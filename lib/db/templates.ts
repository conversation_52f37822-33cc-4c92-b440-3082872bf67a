import { prisma } from '../prisma';
import { Prisma } from '@prisma/client/runtime/library';

// Template operations
export const templateOperations = {
  async findPublic() {
    return prisma.template.findMany({
      where: { userId: null },
      orderBy: { name: 'asc' },
    });
  },

  async findByUser(userId: string) {
    return prisma.template.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async create(input: {
    name: string;
    description?: string;
    definition: Record<string, unknown>;
    userId: string;
  }) {
    return prisma.template.create({
      data: {
        ...input,
        definition: input.definition as Prisma.JsonValue, // Prisma expects JsonValue
      },
    });
  },

  async update(
    id: string,
    input: {
      name?: string;
      description?: string;
      definition?: Record<string, unknown>;
    },
    userId: string,
  ) {
    // First verify the template belongs to the user and is not public
    const existingTemplate = await prisma.template.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingTemplate) {
      throw new Error('Template not found or not editable');
    }

    return prisma.template.update({
      where: { id },
      data: {
        ...input,
        definition: input.definition ? (input.definition as Prisma.JsonValue) : undefined,
        updatedAt: new Date(),
      },
    });
  },

  async findById(id: string) {
    return prisma.template.findUnique({
      where: { id },
    });
  },

  async findByIdAndUserId(id: string, userId: string) {
    return prisma.template.findUnique({
      where: { id, userId },
    });
  },

  async delete(id: string, userId: string) {
    // First verify the template belongs to the user and is not public
    const existingTemplate = await prisma.template.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingTemplate) {
      throw new Error('Template not found or not deletable');
    }

    return prisma.template.delete({
      where: { id },
    });
  },
};
