import { SubscriptionPlan } from '@/app/types';
import { prisma } from '../prisma';

// User operations
export const userOperations = {
  async findByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email },
    });
  },

  async findById(userId: string) {
    return prisma.user.findUnique({
      where: { id: userId },
    });
  },

  async create(data: {
    email: string;
    name?: string;
    image?: string;
    credits?: number;
    creditResetDate?: Date;
  }) {
    return prisma.user.create({
      data,
    });
  },

  async update(
    userId: string,
    data: {
      email?: string;
      name?: string;
      image?: string;
    },
  ) {
    return prisma.user.update({
      where: { id: userId },
      data,
    });
  },

  async delete(userId: string) {
    return prisma.user.delete({
      where: { id: userId },
    });
  },

  async updateSubscription(
    userId: string,
    plan: SubscriptionPlan,
    stripeCustomerId?: string,
    stripeSubscriptionId?: string,
    stripePriceId?: string,
    stripeCurrentPeriodEnd?: Date,
    stripeSubscriptionStatus?: string,
  ) {
    return prisma.user.update({
      where: { id: userId },
      data: {
        plan,
        stripeCustomerId,
        stripeSubscriptionId,
        stripePriceId,
        stripeCurrentPeriodEnd,
        stripeSubscriptionStatus,
      },
    });
  },

  async checkCreditBalance(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        credits: true,
        plan: true,
      },
    });
    return user;
  },

  async hasCredits(userId: string, requiredCredits: number) {
    const user = await this.checkCreditBalance(userId);
    return user ? user.credits >= requiredCredits : false;
  },
};
