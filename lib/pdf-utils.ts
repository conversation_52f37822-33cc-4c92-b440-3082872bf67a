/**
 * Server-side PDF utilities for page count verification
 */

import { getDocumentProxy, getMeta } from 'unpdf';

/**
 * Helper function to safely parse PDF dates
 */
function parsePDFDate(dateString: string): Date | undefined {
  try {
    // PDF dates can be in various formats, try to parse them safely
    if (!dateString) return undefined;

    // Remove PDF date prefix if present (D:YYYYMMDDHHmmSSOHH'mm')
    const cleanDate = dateString.replace(/^D:/, '');

    // Try to parse as ISO date first
    const date = new Date(cleanDate);
    if (!isNaN(date.getTime())) {
      return date;
    }

    // If that fails, try parsing as PDF date format
    if (cleanDate.length >= 8) {
      const year = parseInt(cleanDate.substring(0, 4));
      const month = parseInt(cleanDate.substring(4, 6)) - 1; // Month is 0-based
      const day = parseInt(cleanDate.substring(6, 8));

      if (year && month >= 0 && day) {
        return new Date(year, month, day);
      }
    }

    return undefined;
  } catch {
    return undefined;
  }
}

export interface PDFInfo {
  pageCount: number;
  title?: string;
  author?: string;
  subject?: string;
  creator?: string;
  producer?: string;
  creationDate?: Date;
  modificationDate?: Date;
}

/**
 * Verify PDF page count from a buffer
 * @param buffer PDF file buffer
 * @returns Promise<PDFInfo> PDF information including page count
 */
export async function verifyPDFPageCount(buffer: Buffer): Promise<PDFInfo> {
  try {
    // Parse the PDF using unpdf
    const uint8Array = new Uint8Array(buffer);
    const pdf = await getDocumentProxy(uint8Array);
    const { info } = await getMeta(pdf);

    return {
      pageCount: pdf.numPages,
      title: info?.Title || undefined,
      author: info?.Author || undefined,
      subject: info?.Subject || undefined,
      creator: info?.Creator || undefined,
      producer: info?.Producer || undefined,
      creationDate: info?.CreationDate
        ? parsePDFDate(info.CreationDate)
        : undefined,
      modificationDate: info?.ModDate ? parsePDFDate(info.ModDate) : undefined,
    };
  } catch (error) {
    console.error('Error verifying PDF page count:', error);
    throw new Error(
      `Failed to verify PDF: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}

/**
 * Verify PDF page count from a URL
 * @param url PDF file URL
 * @returns Promise<PDFInfo> PDF information including page count
 */
export async function verifyPDFPageCountFromUrl(url: string): Promise<PDFInfo> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(
        `Failed to fetch PDF from URL: ${response.status} ${response.statusText}`
      );
    }

    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    return verifyPDFPageCount(buffer);
  } catch (error) {
    console.error('Error verifying PDF page count from URL:', error);
    throw new Error(
      `Failed to verify PDF from URL: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    );
  }
}

/**
 * Validate that a client-provided page count matches the actual PDF page count
 * @param buffer PDF file buffer
 * @param clientPageCount Page count provided by client
 * @returns Promise<{ isValid: boolean; actualPageCount: number; clientPageCount: number }>
 */
export async function validateClientPageCount(
  buffer: Buffer,
  clientPageCount: number
): Promise<{
  isValid: boolean;
  actualPageCount: number;
  clientPageCount: number;
}> {
  const pdfInfo = await verifyPDFPageCount(buffer);

  return {
    isValid: pdfInfo.pageCount === clientPageCount,
    actualPageCount: pdfInfo.pageCount,
    clientPageCount,
  };
}
