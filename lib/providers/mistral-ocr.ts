import { Mistral } from '@mistralai/mistralai';
import type { AIOptions, AIResult } from '../ai';

export async function callMistralOCRAPI(options: AIOptions): Promise<AIResult> {
  const apiKey = process.env.MISTRAL_API_KEY;
  if (!apiKey) {
    throw new Error('MISTRAL_API_KEY is not set in environment variables');
  }

  if (!options.templateSchema) {
    throw new Error('`templateSchema` is required');
  }

  try {
    const mistral = new Mistral({ apiKey });

    const ocrResult = await mistral.ocr.process({
      model: 'mistral-ocr-latest',
      pages: Array.from({ length: 8 }, (_, i) => i),
      document: {
        type: 'document_url',
        documentUrl: options.fileUrl,
      },
      documentAnnotationFormat: {
        type: 'json_schema',
        jsonSchema: {
          name: 'document_annotation',
          schemaDefinition: options.templateSchema,
          strict: true,
        },
      },
    });

    const structuredData = JSON.parse(ocrResult.documentAnnotation || '{}');

    return {
      pageCount: ocrResult.usageInfo.pagesProcessed,
      extractedData: structuredData,
      confidence: 1, // not available for now
    };
  } catch (error) {
    console.error('Mistral OCR API error:', error);
    if (error instanceof Error) {
      throw new Error(`Mistral OCR processing failed: ${error.message}`);
    }
    throw new Error(`Mistral OCR processing failed: ${String(error)}`);
  }
}
