import OpenAI from 'openai';
import type { AIOptions, AIResult } from '@/lib/ai';

const systemPrompt = `
You are a precise data extraction agent.

Your job:
- Analyze an image or document from a given URL.
- Extract structured data matching a given JSON schema.
- Estimate the number of pages (images = 1 page).
- Estimate your confidence level in the extraction (0–1 scale).

Your response MUST be a single valid JSON object like this:

{
  "pageCount": number,
  "confidence": number,
  "data": { ... extracted data ... }
}

Rules:
- If it's an image, pageCount = 1.
- If it's a document with multiple pages (like PDF), give a best guess.
- Confidence = how sure you are that the extracted data is correct (0.0 to 1.0).
- Do NOT include any explanation.
- Do NOT format the JSON with comments or extra text.
`;

function buildUserPrompt(schema: Record<string, unknown>): string {
  return `
Extract data using the following schema:

${JSON.stringify(schema, null, 2)}

Return a JSON object with:
- "pageCount": number (1 for images)
- "confidence": number (0.0–1.0)
- "data": object of extracted data

Only return valid JSON.
`;
}

export async function callOpenAIVisionAPI({
  fileUrl,
  templateSchema,
}: AIOptions): Promise<AIResult> {
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new Error('OPENAI_API_KEY is not set in environment variables');
  }

  if (!templateSchema) {
    throw new Error('`templateSchema` is required');
  }

  try {
    const openai = new OpenAI({ apiKey });

    const userPrompt = buildUserPrompt(templateSchema);

    const response = await openai.chat.completions.create({
      model: 'gpt-4.1',
      temperature: 0.2,
      messages: [
        { role: 'system', content: systemPrompt },
        {
          role: 'user',
          content: [
            { type: 'text', text: userPrompt },
            {
              type: 'image_url',
              image_url: {
                url: fileUrl,
              },
            },
          ],
        },
      ],
    });

    const content = response.choices[0]?.message?.content;
    const parsedContent = JSON.parse(content ?? '{}');

    return {
      pageCount: parsedContent.pageCount ?? 1,
      extractedData: parsedContent.data ?? {},
      confidence: parsedContent.confidence ?? 0,
    };
  } catch (error) {
    console.error('OpenAI API error:', error);
    if (error instanceof Error) {
      throw new Error(`OpenAI processing failed: ${error.message}`);
    }
    throw new Error(`OpenAI processing failed: ${String(error)}`);
  }
}
