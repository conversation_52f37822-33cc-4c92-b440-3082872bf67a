import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
  S3ClientConfig,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const isLocal = process.env.AWS_S3_LOCAL === 'true';

// Initialize S3 client
const s3ClientOptions: S3ClientConfig = {
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
};

if (isLocal) {
  s3ClientOptions.endpoint =
    process.env.AWS_S3_LOCAL_ENDPOINT || 'http://localhost:4566';
  s3ClientOptions.forcePathStyle = true;
}

const s3Client = new S3Client(s3ClientOptions);

const BUCKET_NAME = process.env.AWS_S3_BUCKET!;

/**
 * Generate a presigned URL for uploading a file to S3
 */
export async function generatePresignedUploadUrl(
  key: string,
  fileType: string,
  fileSize: number,
  expiresIn: number = 3600, // 1 hour default
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: fileType,
    ContentLength: fileSize,
  });

  const presignedUrl = await getSignedUrl(s3Client, command, {
    expiresIn,
  });

  return presignedUrl;
}

/**
 * Upload a file directly to S3
 */
export async function uploadFileToS3(
  key: string,
  fileBuffer: Buffer,
  contentType: string,
): Promise<void> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    Body: fileBuffer,
    ContentType: contentType,
  });

  await s3Client.send(command);
}

/**
 * Generate a unique file key for S3 storage
 */
export function generateFileKey(
  userId: string,
  jobId: string,
  originalFilename: string,
): string {
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = originalFilename.split('.').pop();

  return `${userId}/${jobId}-${randomString}.${extension}`;
}

/**
 * Get the public URL for a file in S3
 */
export function getFileUrl(key: string): string {
  const isLocal = process.env.AWS_S3_LOCAL === 'true';
  if (isLocal) {
    // LocalStack endpoint (default: http://localhost:4566)
    const endpoint =
      process.env.AWS_S3_LOCAL_ENDPOINT || 'http://localhost:4566';
    return `${endpoint}/${BUCKET_NAME}/${key}`;
  }
  return `https://${BUCKET_NAME}.s3.${
    process.env.AWS_REGION || 'us-east-1'
  }.amazonaws.com/${key}`;
}

/**
 * Extract S3 key from file URL
 */
export function extractKeyFromUrl(url: string): string {
  const isLocal = process.env.AWS_S3_LOCAL === 'true';

  if (isLocal) {
    // LocalStack URL format: http://localhost:4566/bucket-name/key
    const urlParts = url.split('/');
    const bucketIndex = urlParts.findIndex((part) => part === BUCKET_NAME);
    if (bucketIndex === -1 || bucketIndex === urlParts.length - 1) {
      throw new Error('Invalid local S3 URL format');
    }
    return urlParts.slice(bucketIndex + 1).join('/');
  } else {
    // AWS S3 URL format: https://bucket-name.s3.region.amazonaws.com/key
    const url_obj = new URL(url);
    const key = url_obj.pathname.substring(1); // Remove leading slash
    if (!key) {
      throw new Error('Invalid S3 URL format');
    }
    return key;
  }
}

/**
 * Delete a file from S3 using its URL
 */
export async function deleteFileFromUrl(url: string): Promise<void> {
  const key = extractKeyFromUrl(url);
  await deleteFileFromS3(key);
}

/**
 * Delete a file from S3 using its key
 */
export async function deleteFileFromS3(key: string): Promise<void> {
  const command = new DeleteObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
  });

  await s3Client.send(command);
}

/**
 * Validate file type and size
 */
export function validateFile(file: { type: string; size: number }) {
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];

  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error(
      'File type not allowed. Please upload PDF, JPEG, PNG, GIF, or WebP files.',
    );
  }

  if (file.size > maxSize) {
    throw new Error('File size too large. Maximum size is 10MB.');
  }

  return true;
}
