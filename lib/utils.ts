import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { User } from '@/lib/db';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Check if user has sufficient credits for the operation
 */
export function checkCredits(user: User, requiredCredits: number = 1) {
  if (user.credits < requiredCredits) {
    throw new Error(
      `Insufficient credits. Required: ${requiredCredits}, Available: ${user.credits}`,
    );
  }
}
