
import { prisma } from '@/lib/prisma';
import crypto from 'crypto';
import { Job } from '@prisma/client';

export async function sendWebhook(eventType: string, payload: Job, userId: string) {
  const endpoints = await prisma.webhookEndpoint.findMany({
    where: {
      userId,
      enabled: true,
      events: {
        has: eventType,
      },
    },
  });

  for (const endpoint of endpoints) {
    const signature = crypto
      .createHmac('sha256', endpoint.secret)
      .update(JSON.stringify(payload))
      .digest('hex');

    try {
      const response = await fetch(endpoint.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Unfurl-Signature': signature,
        },
        body: JSON.stringify(payload),
      });

      await prisma.webhookEvent.create({
        data: {
          eventType,
          payload,
          success: response.ok,
          statusCode: response.status,
          response: await response.text(),
          endpointId: endpoint.id,
          userId,
        },
      });
    } catch (error) {
      console.error(`Failed to send webhook to ${endpoint.url}`, error);
      await prisma.webhookEvent.create({
        data: {
          eventType,
          payload,
          success: false,
          response: error instanceof Error ? error.message : 'Unknown error',
          endpointId: endpoint.id,
          userId,
        },
      });
    }
  }
}
