{"name": "unfurl", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "test:unit": "vitest", "test:e2e": "npx playwright test --ui", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate"}, "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@hookform/resolvers": "^5.1.1", "@mistralai/mistralai": "^1.7.2", "@prisma/client": "^6.9.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.81.5", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "@upstash/qstash": "^2.8.1", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.5.0", "better-auth": "^1.2.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "formidable": "^3.5.4", "lucide-react": "^0.514.0", "next": "15.3.3", "openai": "^5.7.0", "pdfjs-dist": "^5.3.31", "pg": "^8.16.2", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^3.0.2", "sonner": "^2.0.5", "svix": "^1.67.0", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "unpdf": "^1.0.6", "uuid": "^11.1.0", "zod": "^3.25.67", "zod-prisma-types": "^3.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.0", "@tailwindcss/postcss": "^4", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9", "eslint-config-next": "15.3.3", "jsdom": "^26.1.0", "tailwindcss": "^4", "tsx": "^4.20.2", "tw-animate-css": "^1.3.4", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.3"}}