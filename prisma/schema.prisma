// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// 1. Define your datasource.
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 2. Define your generator.
generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider = "zod-prisma-types"
}

// 3. Define your application models.

model User {
  id    String  @id @default(cuid())
  email String  @unique
  name  String?
  image String?

  emailVerified Boolean

  // Application specific fields
  plan              String    @default("free") // e.g., "free", "starter", "pro"
  credits           Int       @default(20) // Credit-based system for page processing
  dataRetentionDays Int       @default(1) // User-configurable data retention (1-30 days)
  creditResetDate   DateTime? // The date when a free user's credits should next be reset.

  // Stripe integration fields
  stripeCustomerId         String?   @unique
  stripeSubscriptionId     String?   @unique
  stripePriceId            String?
  stripeCurrentPeriodEnd   DateTime?
  stripeSubscriptionStatus String? // e.g., "active", "past_due", "canceled"

  // Relations
  jobs             Job[]
  apiKeys          APIKey[]
  templates        Template[]
  webhookEndpoints WebhookEndpoint[]
  webhookEvents    WebhookEvent[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sessions Session[]
  accounts Account[]

  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

// The Job model tracks each document extraction process.
model Job {
  id          String @id @default(cuid())
  fileName    String
  fileUrl     String // The URL to the document in cloud storage (e.g., S3).
  pageCount   Int    @default(0)
  creditsUsed Int    @default(0)
  status      String @default("PENDING") // PENDING, PROCESSING, COMPLETED, FAILED, PURGED

  // This field holds the sensitive extracted data.
  // It is optional so we can set it to null after the retention period.
  extractedData Json?

  // Relation to the User
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // A job can optionally be processed using a specific template.
  // If templateId is null, a pre-built model was used.
  templateId String?
  template   Template? @relation(fields: [templateId], references: [id], onUpdate: NoAction, onDelete: SetNull)

  // Error handling
  error      String?
  retryCount Int     @default(0)

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  completedAt DateTime?

  @@map("job")
}

// The Template model stores user-defined extraction rules using JSON Schema.
model Template {
  id          String  @id @default(cuid())
  name        String
  description String?

  // The structure of the template, defined as a JSON Schema.
  // This tells the Mistral API what fields to extract and what their types should be.
  // e.g., { "type": "object", "properties": { "invoice_number": { "type": "string" }, "total": { "type": "number" } } }
  definition Json

  // Relation to the User. For public templates, it should be null.
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relation to Jobs that use this template
  jobs Job[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("template")
}

// The APIKey model allows developers to access the Unfurl API.
model APIKey {
  id   String @id @default(cuid())
  key  String @unique // This will store a HASH of the actual key, not the key itself.
  name String // A user-friendly name for the key, e.g., "My Production App"

  // Relation to the User
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  expiresAt  DateTime?
  lastUsedAt DateTime?

  @@map("apiKey")
}

// The WebhookEndpoint model stores user-configured URLs for receiving event notifications.
model WebhookEndpoint {
  id     String @id @default(cuid())
  url    String // The URL to send the webhook payload to.
  secret String // A secret key used for signing the webhook payload, so the user can verify its authenticity.

  // An array of event types the user wants to subscribe to.
  // e.g., ["job.completed", "job.failed"]
  events  String[]
  enabled Boolean  @default(true)

  // Relation to the User
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relation to logs of events sent to this endpoint
  webhookEvents WebhookEvent[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("webhookEndpoint")
}

// The WebhookEvent model logs every outgoing webhook attempt.
// This is crucial for debugging and showing users a delivery history.
model WebhookEvent {
  id         String  @id @default(cuid())
  eventType  String // e.g., "job.completed"
  payload    Json // The JSON payload that was sent.
  success    Boolean // Was the delivery successful (i.e., did we get a 2xx response)?
  statusCode Int? // The HTTP status code returned by the user's endpoint.
  response   String? // The response body from the user's endpoint.

  // Relation to the endpoint it was sent to
  endpointId String
  endpoint   WebhookEndpoint @relation(fields: [endpointId], references: [id], onDelete: Cascade)

  // Relation to the user who owns the endpoint
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())

  @@map("webhookEvent")
}
