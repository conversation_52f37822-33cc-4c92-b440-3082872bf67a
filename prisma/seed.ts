import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Define template configurations
  const generalTemplateData = {
    id: 'general',
    name: 'General Document',
    description: 'Extract general information from any document',
    definition: {
      title: { type: 'string', required: false, description: 'Document title' },
      content: { type: 'string', required: false, description: 'Main content' },
      date: { type: 'string', required: false, description: 'Document date' },
      amount: {
        type: 'number',
        required: false,
        description: 'Any monetary amount',
      },
    },
  };

  const generalTemplate = await prisma.template.upsert({
    where: { id: generalTemplateData.id },
    update: generalTemplateData,
    create: generalTemplateData,
  });

  const invoiceTemplateData = {
    id: 'invoice-template',
    name: 'Invoice Template',
    description: 'Standard invoice data extraction template',
    definition: {
      invoice_number: {
        type: 'string',
        required: true,
        description: 'The unique invoice number or ID',
      },
      date: { type: 'date', required: true, description: 'The invoice date' },
      due_date: {
        type: 'date',
        required: false,
        description: 'The payment due date',
      },
      vendor_name: {
        type: 'string',
        required: true,
        description: 'The name of the vendor or company issuing the invoice',
      },
      vendor_address: {
        type: 'string',
        required: false,
        description: "The vendor's billing address",
      },
      total_amount: {
        type: 'number',
        required: true,
        description: 'The total amount due including taxes',
      },
      tax_amount: {
        type: 'number',
        required: false,
        description: 'The total tax amount',
      },
      line_items: {
        type: 'array',
        required: false,
        description: 'Individual items or services listed on the invoice',
        items: {
          type: 'object',
          properties: {
            description: {
              type: 'string',
              description: 'Description of the item or service',
            },
            quantity: { type: 'number', description: 'Quantity of items' },
            unit_price: { type: 'number', description: 'Price per unit' },
            total: {
              type: 'number',
              description: 'Total price for this line item',
            },
          },
        },
      },
    },
  };

  // Create pre-built templates for common document types
  const invoiceTemplate = await prisma.template.upsert({
    where: { id: invoiceTemplateData.id },
    update: invoiceTemplateData,
    create: invoiceTemplateData,
  });

  const receiptTemplateData = {
    id: 'receipt-template',
    name: 'Receipt Template',
    description: 'Standard receipt data extraction template',
    definition: {
      merchant_name: {
        type: 'string',
        required: true,
        description: 'The name of the store or merchant',
      },
      date: {
        type: 'date',
        required: true,
        description: 'The date of the transaction',
      },
      time: {
        type: 'time',
        required: false,
        description: 'The time of the transaction',
      },
      total_amount: {
        type: 'number',
        required: true,
        description: 'The total amount paid',
      },
      tax_amount: {
        type: 'number',
        required: false,
        description: 'The total tax amount',
      },
      payment_method: {
        type: 'string',
        required: false,
        description: 'The method of payment (cash, card, etc.)',
      },
      items: {
        type: 'array',
        required: false,
        description: 'Items purchased',
        items: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: 'Name of the purchased item',
            },
            price: { type: 'number', description: 'Price of the item' },
            quantity: { type: 'number', description: 'Quantity purchased' },
          },
        },
      },
    },
  };

  const receiptTemplate = await prisma.template.upsert({
    where: { id: receiptTemplateData.id },
    update: receiptTemplateData,
    create: receiptTemplateData,
  });

  const businessCardTemplateData = {
    id: 'business-card-template',
    name: 'Business Card Template',
    description: 'Business card contact information extraction template',
    definition: {
      name: {
        type: 'string',
        required: true,
        description: "The person's full name",
      },
      title: {
        type: 'string',
        required: false,
        description: 'Job title or position',
      },
      company: {
        type: 'string',
        required: false,
        description: 'Company or organization name',
      },
      email: { type: 'email', required: false, description: 'Email address' },
      phone: { type: 'phone', required: false, description: 'Phone number' },
      address: {
        type: 'string',
        required: false,
        description: 'Business address',
      },
      website: {
        type: 'url',
        required: false,
        description: 'Company website URL',
      },
    },
  };

  const businessCardTemplate = await prisma.template.upsert({
    where: { id: businessCardTemplateData.id },
    update: businessCardTemplateData,
    create: businessCardTemplateData,
  });

  console.log('✅ Created pre-built templates:', {
    general: generalTemplate.id,
    invoice: invoiceTemplate.id,
    receipt: receiptTemplate.id,
    businessCard: businessCardTemplate.id,
  });

  console.log('🎉 Database seeded successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
