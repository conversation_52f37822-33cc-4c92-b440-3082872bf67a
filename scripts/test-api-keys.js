#!/usr/bin/env node

/**
 * Simple test script to verify API key functionality
 * Run with: node scripts/test-api-keys.js
 */

const crypto = require('crypto');

// Test API key generation
function generateApiKey() {
  const keyBytes = crypto.randomBytes(32);
  return `unfurl_${keyBytes.toString('hex')}`;
}

// Test API key validation
function validateApiKeyFormat(key) {
  if (!key || typeof key !== 'string') {
    return false;
  }

  // Should start with 'unfurl_'
  if (!key.startsWith('unfurl_')) {
    return false;
  }

  // Should be 71 characters total (unfurl_ = 7 chars + 64 hex chars)
  if (key.length !== 71) {
    return false;
  }

  // Should only contain valid hex characters after the prefix
  const hexPart = key.substring(7);
  const hexRegex = /^[a-f0-9]+$/i;

  return hexRegex.test(hexPart);
}

// Test API key preview generation
function generateKeyPreview(key) {
  if (!key || key.length < 12) {
    return key;
  }
  return `${key.slice(0, 8)}...${key.slice(-4)}`;
}

// Run tests
console.log('🔑 Testing API Key Functionality');
console.log('================================');

// Test 1: Generate API keys
console.log('\n1. Testing API key generation...');
const keys = [];
for (let i = 0; i < 5; i++) {
  const key = generateApiKey();
  keys.push(key);
  console.log(`   Generated key ${i + 1}: ${key}`);
}

// Test 2: Validate API key format
console.log('\n2. Testing API key validation...');
keys.forEach((key, index) => {
  const isValid = validateApiKeyFormat(key);
  console.log(`   Key ${index + 1} valid: ${isValid ? '✅' : '❌'}`);
});

// Test 3: Test invalid keys
console.log('\n3. Testing invalid key detection...');
const invalidKeys = [
  '',
  'invalid_key',
  'unfurl_',
  'unfurl_toolshort',
  'notunfurl_' + crypto.randomBytes(32).toString('hex'),
  'unfurl_' + 'xyz123' // not valid hex
];

invalidKeys.forEach((key, index) => {
  const isValid = validateApiKeyFormat(key);
  console.log(`   Invalid key ${index + 1}: ${isValid ? '❌ (should be invalid)' : '✅ (correctly invalid)'}`);
  console.log(`     Key: "${key}"`);
});

// Test 4: Test key preview generation
console.log('\n4. Testing key preview generation...');
keys.forEach((key, index) => {
  const preview = generateKeyPreview(key);
  console.log(`   Key ${index + 1} preview: ${preview}`);
});

// Test 5: Uniqueness test
console.log('\n5. Testing key uniqueness...');
const uniqueKeys = new Set(keys);
console.log(`   Generated ${keys.length} keys, ${uniqueKeys.size} unique: ${keys.length === uniqueKeys.size ? '✅' : '❌'}`);

// Test 6: Security considerations
console.log('\n6. Testing security considerations...');
const key = generateApiKey();
console.log(`   Full key length: ${key.length} characters`);
console.log(`   Entropy: ${crypto.randomBytes(32).length * 8} bits`);
console.log(`   Preview masks sensitive data: ${generateKeyPreview(key)}`);

console.log('\n🎉 API Key tests completed!');
console.log('\nNext steps:');
console.log('- Start the development server: npm run dev');
console.log('- Navigate to /api-keys in your browser');
console.log('- Test creating and managing API keys through the UI');