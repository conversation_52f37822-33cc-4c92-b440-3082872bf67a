import {
  describe,
  it,
  expect,
  beforeEach,
  afterEach,
  beforeAll,
  afterAll,
} from 'vitest';
import { templateOperations } from '@/lib/db/templates';
import { prisma } from '@/lib/prisma';

describe('Template Operations', () => {
  const testUserId = 'test-user-123';
  const otherUserId = 'other-user-id';
  let createdTemplateIds: string[] = [];

  beforeAll(async () => {
    await prisma.user.createMany({
      data: [
        {
          id: testUserId,
          name: 'Test User',
          email: '<EMAIL>',
        },
        {
          id: otherUserId,
          name: 'Other Test User',
          email: '<EMAIL>',
        },
      ],
    });
  });

  afterAll(async () => {
    await prisma.user.deleteMany({
      where: { id: { in: [testUserId, otherUserId] } },
    });
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await prisma.template.deleteMany({
      where: { userId: testUserId },
    });
    createdTemplateIds = [];
  });

  afterEach(async () => {
    // Clean up created templates
    if (createdTemplateIds.length > 0) {
      await prisma.template.deleteMany({
        where: { id: { in: createdTemplateIds } },
      });
    }
  });

  describe('create', () => {
    it('should create a new template', async () => {
      const templateData = {
        name: 'Test Invoice Template',
        description: 'A test template for invoices',
        fields: {
          invoice_number: { type: 'string', required: true },
          total_amount: { type: 'number', required: true },
          date: { type: 'date', required: false },
        },
        userId: testUserId,
      };

      const template = await templateOperations.create(templateData);
      createdTemplateIds.push(template.id);

      expect(template).toBeDefined();
      expect(template.name).toBe(templateData.name);
      expect(template.description).toBe(templateData.description);
      expect(template.userId).toBe(testUserId);
      expect(template.isPublic).toBe(false);
      expect(template.fields).toEqual(templateData.fields);
    });
  });

  describe('update', () => {
    it('should update an existing template', async () => {
      // First create a template
      const originalTemplate = await templateOperations.create({
        name: 'Original Template',
        description: 'Original description',
        fields: {
          field1: { type: 'string', required: true },
        },
        userId: testUserId,
      });
      createdTemplateIds.push(originalTemplate.id);

      // Update the template
      const updateData = {
        name: 'Updated Template',
        description: 'Updated description',
        fields: {
          field1: { type: 'string', required: true },
          field2: { type: 'number', required: false },
        },
      };

      const updatedTemplate = await templateOperations.update(
        originalTemplate.id,
        updateData,
        testUserId,
      );

      expect(updatedTemplate.name).toBe(updateData.name);
      expect(updatedTemplate.description).toBe(updateData.description);
      expect(updatedTemplate.fields).toEqual(updateData.fields);
    });

    it('should throw error when trying to update non-existent template', async () => {
      await expect(
        templateOperations.update(
          'non-existent-id',
          { name: 'Test' },
          testUserId,
        ),
      ).rejects.toThrow('Template not found or not editable');
    });

    it('should throw error when trying to update template of another user', async () => {
      // Create template with different user
      const template = await templateOperations.create({
        name: 'Other User Template',
        fields: { field1: { type: 'string', required: true } },
        userId: otherUserId,
      });
      createdTemplateIds.push(template.id);

      await expect(
        templateOperations.update(template.id, { name: 'Hacked' }, testUserId),
      ).rejects.toThrow('Template not found or not editable');
    });

    it('should throw error when trying to update public template', async () => {
      // Create a public template (this would normally be done by admin)
      const publicTemplate = await prisma.template.create({
        data: {
          name: 'Public Template',
          fields: { field1: { type: 'string', required: true } },
          isPublic: true,
        },
      });
      createdTemplateIds.push(publicTemplate.id);

      await expect(
        templateOperations.update(
          publicTemplate.id,
          { name: 'Hacked' },
          testUserId,
        ),
      ).rejects.toThrow('Template not found or not editable');
    });
  });

  describe('delete', () => {
    it('should delete an existing template', async () => {
      // First create a template
      const template = await templateOperations.create({
        name: 'Template to Delete',
        fields: { field1: { type: 'string', required: true } },
        userId: testUserId,
      });

      // Delete the template
      await templateOperations.delete(template.id, testUserId);

      // Verify it's deleted
      const deletedTemplate = await prisma.template.findUnique({
        where: { id: template.id },
      });
      expect(deletedTemplate).toBeNull();
    });

    it('should throw error when trying to delete non-existent template', async () => {
      await expect(
        templateOperations.delete('non-existent-id', testUserId),
      ).rejects.toThrow('Template not found or not deletable');
    });

    it('should throw error when trying to delete template of another user', async () => {
      const template = await templateOperations.create({
        name: 'Other User Template',
        fields: { field1: { type: 'string', required: true } },
        userId: otherUserId,
      });
      createdTemplateIds.push(template.id);

      await expect(
        templateOperations.delete(template.id, testUserId),
      ).rejects.toThrow('Template not found or not deletable');
    });
  });

  describe('findById', () => {
    it('should find template by id', async () => {
      const template = await templateOperations.create({
        name: 'Findable Template',
        fields: { field1: { type: 'string', required: true } },
        userId: testUserId,
      });
      createdTemplateIds.push(template.id);

      const foundTemplate = await templateOperations.findById(template.id);
      expect(foundTemplate).toBeDefined();
      expect(foundTemplate?.id).toBe(template.id);
      expect(foundTemplate?.name).toBe(template.name);
    });

    it('should return null for non-existent template', async () => {
      const foundTemplate =
        await templateOperations.findById('non-existent-id');
      expect(foundTemplate).toBeNull();
    });
  });

  describe('findByUser', () => {
    it('should find templates by user', async () => {
      // Create multiple templates for the user
      const template1 = await templateOperations.create({
        name: 'User Template 1',
        fields: { field1: { type: 'string', required: true } },
        userId: testUserId,
      });
      const template2 = await templateOperations.create({
        name: 'User Template 2',
        fields: { field2: { type: 'number', required: true } },
        userId: testUserId,
      });
      createdTemplateIds.push(template1.id, template2.id);

      const userTemplates = await templateOperations.findByUser(testUserId);
      expect(userTemplates).toHaveLength(2);
      expect(userTemplates.map((t) => t.name)).toContain('User Template 1');
      expect(userTemplates.map((t) => t.name)).toContain('User Template 2');
    });

    it('should return empty array for user with no templates', async () => {
      const userTemplates = await templateOperations.findByUser(
        'user-with-no-templates',
      );
      expect(userTemplates).toHaveLength(0);
    });
  });
});
